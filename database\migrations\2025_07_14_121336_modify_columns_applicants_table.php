<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applicants', function (Blueprint $table) {
            $table->integer('title_id')->nullable()->change();
            $table->integer('citizenship_type')->nullable()->change();
            $table->integer('gender_cat_id')->nullable()->change();
            $table->integer('civil_status_cat_id')->nullable()->change();
            $table->integer('class_cat_id')->nullable()->change();
            $table->integer('duration')->nullable()->change();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applicants', function (Blueprint $table) {
            $table->integer('title_id')->nullable(false)->change();
            $table->integer('citizenship_type')->nullable(false)->change();
            $table->integer('gender_cat_id')->nullable(false)->change();
            $table->integer('civil_status_cat_id')->nullable(false)->change();
            $table->integer('class_cat_id')->nullable(false)->change();
            $table->integer('duration')->nullable(false)->change();
        });
    }
};
