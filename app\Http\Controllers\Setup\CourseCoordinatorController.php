<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Http\Requests\CourseCoordinatorStoreRequest;
use App\Mail\NewUserPasswordMail;
use App\Models\Course;
use App\Models\CourseCoordinator;
use App\Models\User;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class CourseCoordinatorController extends Controller implements HasMiddleware
{
    public static function middleware(): array
    {
        return [
            'auth',
        ];
    }

    public function CourseCoordinatorIndex()
    {
        Gate::authorize('course.coordinator.list');

        // Call the method to get employee data from the HRMS API
        $empData = $this->fetchEmployeeData();

        // Call the method to get study board chairperson details
        $data = $this->getCourseCoordinatorPersonDetails($empData);

        return view('admin.setups.course_coordinator.index', compact('data'));
    }

    public function CourseCoordinatorAdd()
    {
        Gate::authorize('course.coordinator.create');

        $courses = Course::where('courses.active_status', 1)->where('course_main_category', 41)
            ->whereDoesntHave('courseCoordinator', function ($query) {
                $query->where('active_status', 1);
            })->get();

        $empData = $this->acdemicEmployeeData();
        $categories = $this->getCategories([2]);
        $appartmentTypes = $categories->where('category_type_id', '2');
        return view('admin.setups.course_coordinator.add', compact('courses', 'empData', 'appartmentTypes'));
    }

    private function createUserFromEmployeeData($employeeData)
    {
        $employeeNo = $employeeData[0];
        $email = $employeeData[1];
        $lastname = $employeeData[2];
        $initials = $employeeData[3];

        // Check if user already exists with this email
        $existingUser = User::where('reg_no', $employeeNo)->first();

        if ($existingUser) {
            if ($existingUser->status === 0) {
                // Update status to 1 for inactive user
                $existingUser->status = 1;
                $existingUser->save();
            }

            // Assign Chair-Person role if not already assigned
            if (!$existingUser->hasRole('Course-Coordinator')) {
                $existingUser->assignRole('Course-Coordinator');
            }

            return $existingUser;
        }

        // Create new user if no existing user found
        $data = new User();
        $uppercase = chr(rand(65, 90));
        $lowercase = chr(rand(97, 122));
        $number = rand(0, 9);
        $special = ['@', '#', '$', '%', '&', '*'][rand(0, 5)];
        $remaining = substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 4);
        $code = $uppercase . $lowercase . $number . $special . $remaining;
        $data->reg_no = $employeeNo;
        $data->name =  $initials . ' ' . $lastname;
        $data->email = $email;
        $data->password = bcrypt($code);
        $data->status = 1;
        $data->save();

        $data->assignRole("User", "Course-Coordinator");

        // Send password email to the user
        //Mail::to("<EMAIL>")->send(new NewUserPasswordMail($data->name, $data->email, $code));

        return $data;
    }

    public function CourseCoordinatorStore(CourseCoordinatorStoreRequest $request)
    {
        Gate::authorize('course.coordinator.create');
        CourseCoordinator::create($request->prepareData());

        $selectedEmployee = explode('|', $request->input('emp_no'));
        $this->createUserFromEmployeeData($selectedEmployee);

        $notification = array(
            'message' => 'New Course Coordinator Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('course.coordinator.index')->with($notification);
    }

    public function CourseCoordinatorEdit($id)
    {
        Gate::authorize('course.coordinator.updation');
        $courseCoordinatorId = decrypt($id);
        $courses = Course::where('active_status', 1)->get();
        $empData = $this->acdemicEmployeeData();
        $categories = $this->getCategories([2]);
        $appartmentTypes = $categories->where('category_type_id', '2');
        $editData = CourseCoordinator::find($courseCoordinatorId);
        return view('admin.setups.course_coordinator.edit', compact('editData', 'courses', 'empData', 'appartmentTypes'));
    }

    private function handleCourseCoordinatorRoleUpdate($previousEmpNo, $newEmpNo)
    {
        if ($previousEmpNo !== $newEmpNo) {
            $previousUser = User::where('reg_no', $previousEmpNo)->first();
            if ($previousUser && $previousUser->hasRole('Course-Coordinator')) {
                // Check if user has other active chair person assignments
                $otherActiveAssignments = CourseCoordinator::where('emp_no', $previousEmpNo)
                    ->where('active_status', 1)
                    ->count();

                // Only remove Chair-Person role if this was their last assignment
                if ($otherActiveAssignments <= 1) {
                    $previousUser->removeRole('Course-Coordinator');
                }
            }
        }
    }

    public function CourseCoordinatorUpdate(CourseCoordinatorStoreRequest $request, $id)
    {
        Gate::authorize('course.coordinator.updation');

        $previousEmpNo = $request->input('pre_emp_no');
        $newEmpNo = explode('|', $request->input('emp_no'))[0];

        $this->handleCourseCoordinatorRoleUpdate($previousEmpNo, $newEmpNo);

        $request->persist($id);
        $this->createUserFromEmployeeData(explode('|', $request->input('emp_no')));

        $notification = array(
            'message' => 'Course Coordinator data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('course.coordinator.index')->with($notification);
    }

    public function CourseCoordinatorDelete($id)
    {
        Gate::authorize('course.coordinator.delete');
        $courseCoordinatorId = decrypt($id);
        $data = CourseCoordinator::find($courseCoordinatorId);
        $data->delete();

        $notification = array(
            'message' => 'Course Coordinator Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('course.coordinator.index')->with($notification);
    }


    private function fetchEmployeeData()
    {
        $empIDs = CourseCoordinator::pluck('emp_no');

        // Make API request to HRMS
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
            'x-api-key' => 'bf919ee9-09fa-43f5-94d6-3339888f0f5f',
        ])->post('https://hrms.sjp.ac.lk/api/emp/fgs/study/board/chair/person/list', ['empIDs' => $empIDs]);

        // Decode the response to an associative array
        return json_decode($empDetails, true);
    }

    private function getCourseCoordinatorPersonDetails($empData)
    {
        $empNo = Auth()->user()->reg_no;

        if (Auth()->user()->hasRole(['Super-Admin', 'Admin', 'Admin-Officer', 'FGS-Admin'])) {
            $data = CourseCoordinator::join('courses', 'courses.id', '=', 'course_coordinators.course_code')
                ->select('course_coordinators.*', 'courses.course_name')
                ->where('course_coordinators.active_status', 1)
                ->where('courses.active_status', 1)
                ->orderBy('course_code')
                ->get();
        } elseif (Auth()->user()->hasRole(['Subject-Clerk', 'Subject-Assistant'])) {
            $data = CourseCoordinator::join('courses', 'courses.id', '=', 'course_coordinators.course_code')
                ->join('course_operators', 'courses.id', '=', 'course_operators.course_code')
                ->select('course_coordinators.*', 'courses.course_name')
                ->where('course_coordinators.active_status', 1)
                ->where('courses.active_status', 1)
                ->where('course_operators.emp_no', $empNo)
                ->orderBy('course_code')
                ->get();
        } else {
            return collect([]);
        }

        return $data->map(function ($item) use ($empData) {
            $empID = $item['emp_no'];
            $employeeInfo = collect($empData)->firstWhere('employee_no', $empID);

            // Map the additional employee info
            $item['LName'] = $employeeInfo['last_name'] ?? null;
            $item['initial'] = $employeeInfo['initials'] ?? null;
            $item['title'] = $employeeInfo['title'] ?? null;
            $item['department'] = $employeeInfo['department_name'] ?? null;
            $item['status'] = $employeeInfo['employee_status_id'] ?? null;
            $item['email'] = $employeeInfo['email'] ?? null;
            $item['mobile_no'] = $employeeInfo['mobile_no'] ?? null;

            return $item;
        });
    }

    private function acdemicEmployeeData()
    {
        // Make API request to HRMS
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
            'x-api-key' => 'bf919ee9-09fa-43f5-94d6-3339888f0f5f',
        ])->post('https://hrms.sjp.ac.lk/api/emp/fgs/study/board/chair/person');

        // Decode the response to an associative array
        return json_decode($empDetails, true);
    }
}
