<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('registered_students', function (Blueprint $table) {
            $table->integer('current_year')->default(0)->after('convocation_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('registered_students', function (Blueprint $table) {
            $table->dropColumn('current_year');
        });
    }
};
