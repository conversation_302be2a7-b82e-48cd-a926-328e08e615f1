<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Http\Requests\StudyBoardChairPersonStoreRequest;
use App\Mail\NewUserPasswordMail;
use App\Models\ChairPerson;
use App\Models\StudyBoard;
use App\Models\User;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class StudyBoardChairPersonController extends Controller implements HasMiddleware
{
    public static function middleware(): array
    {
        return [
            'auth',
        ];
    }

    public function StudyBoardChairPersonIndex()
    {
        Gate::authorize('study.board.chair.person.list');

        // Call the method to get employee data from the HRMS API
        $empData = $this->fetchEmployeeData();

        // Call the method to get study board chairperson details
        $data = $this->getStudyBoardChairPersonDetails($empData);

        return view('admin.setups.study_board_chair_person.index', compact('data'));
    }

    public function StudyBoardChairPersonAdd()
    {
        Gate::authorize('study.board.chair.person.create');

        $studyBoards = StudyBoard::where('study_boards.active_status', 1)
        ->whereDoesntHave('chairPeople', function ($query) {
            $query->where('active_status', 1);
        })->get();

        $empData = $this->acdemicEmployeeData();
        $categories = $this->getCategories([2]);
        $appartmentTypes = $categories->where('category_type_id', '2');
        return view('admin.setups.study_board_chair_person.add', compact('studyBoards','empData','appartmentTypes'));
    }

    private function createUserFromEmployeeData($employeeData)
    {
        $employeeNo = $employeeData[0];
        $email = $employeeData[1];
        $lastname = $employeeData[2];
        $initials = $employeeData[3];

        // Check if user already exists with this email
        $existingUser = User::where('reg_no', $employeeNo)->first();

        if ($existingUser) {
            if ($existingUser->status === 0) {
                // Update status to 1 for inactive user
                $existingUser->status = 1;
                $existingUser->save();
            }

            // Assign Chair-Person role if not already assigned
            if (!$existingUser->hasRole('Chair-Person')) {
                $existingUser->assignRole('Chair-Person');
            }

            return $existingUser;
        }

        // Create new user if no existing user found
        $data = new User();
        $uppercase = chr(rand(65, 90));
        $lowercase = chr(rand(97, 122));
        $number = rand(0, 9);
        $special = ['@', '#', '$', '%', '&', '*'][rand(0, 5)];
        $remaining = substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 4);
        $code = $uppercase . $lowercase . $number . $special . $remaining;
        $data->reg_no = $employeeNo;
        $data->name =  $initials . ' ' . $lastname;
        $data->email = $email;
        $data->password = bcrypt($code);
        $data->status = 1;
        $data->save();

        $data->assignRole("User","Chair-Person");

        // Send password email to the user
        //Mail::to("<EMAIL>")->send(new NewUserPasswordMail($data->name, $data->email, $code));

        return $data;
    }

    public function StudyBoardChairPersonStore(StudyBoardChairPersonStoreRequest $request)
    {
        Gate::authorize('study.board.chair.person.create');
        ChairPerson::create($request->prepareData());

        $selectedEmployee = explode('|', $request->input('emp_no'));
        $this->createUserFromEmployeeData($selectedEmployee);

        $notification = array(
            'message' => 'New Study Board Chair Person Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('study.board.chair.person.index')->with($notification);
    }

    public function StudyBoardChairPersonEdit($id)
    {
        Gate::authorize('study.board.chair.person.updation');
        $studyBoardChairPersonId = decrypt($id);
        $studyBoards = StudyBoard::where('active_status', 1)->get();
        $empData = $this->acdemicEmployeeData();
        $categories = $this->getCategories([2]);
        $appartmentTypes = $categories->where('category_type_id', '2');
        $editData = ChairPerson::find($studyBoardChairPersonId);
        return view('admin.setups.study_board_chair_person.edit', compact('editData','studyBoards','empData','appartmentTypes'));
    }

    private function handleChairPersonRoleUpdate($previousEmpNo, $newEmpNo)
    {
        if ($previousEmpNo !== $newEmpNo) {
            $previousUser = User::where('reg_no', $previousEmpNo)->first();
            if ($previousUser && $previousUser->hasRole('Chair-Person')) {
                // Check if user has other active chair person assignments
                $otherActiveAssignments = ChairPerson::where('emp_no', $previousEmpNo)
                    ->where('active_status', 1)
                    ->count();

                // Only remove Chair-Person role if this was their last assignment
                if ($otherActiveAssignments <= 1) {
                    $previousUser->removeRole('Chair-Person');
                }
            }
        }
    }

    public function StudyBoardChairPersonUpdate(StudyBoardChairPersonStoreRequest $request, $id)
    {
        Gate::authorize('study.board.chair.person.updation');

        $previousEmpNo = $request->input('pre_emp_no');
        $newEmpNo = explode('|', $request->input('emp_no'))[0];

        $this->handleChairPersonRoleUpdate($previousEmpNo, $newEmpNo);

        $request->persist($id);
        $this->createUserFromEmployeeData(explode('|', $request->input('emp_no')));

        $notification = array(
            'message' => 'Study Board Chair Person data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('study.board.chair.person.index')->with($notification);
    }

    public function StudyBoardChairPersonDelete($id)
    {
        Gate::authorize('study.board.chair.person.delete');
        $studyBoardChairPersonId = decrypt($id);
        $data = ChairPerson::find($studyBoardChairPersonId);
        $data->delete();

        $notification = array(
            'message' => 'Study Board Chair Person Permanently Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('study.board.chair.person.index')->with($notification);
    }

    private function fetchEmployeeData()
    {
        $empIDs = ChairPerson::pluck('emp_no');

        // Make API request to HRMS
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
            'x-api-key' => 'bf919ee9-09fa-43f5-94d6-3339888f0f5f',
        ])->post('https://hrms.sjp.ac.lk/api/emp/fgs/study/board/chair/person/list', ['empIDs' => $empIDs]);

        // Decode the response to an associative array
        return json_decode($empDetails, true);
    }

    private function getStudyBoardChairPersonDetails($empData)
    {
        // Join the chairperson and study board data, map it with the HRMS data
        return ChairPerson::join('study_boards', 'study_boards.id', '=', 'chair_people.study_board_id')
            ->select('chair_people.*', 'study_boards.name')
            ->where('chair_people.active_status', 1)
            ->where('study_boards.active_status', 1)
            ->orderBy('study_board_id')
            ->get()
            ->map(function ($item) use ($empData) {
                $empID = $item['emp_no'];

                $employeeInfo = collect($empData)->firstWhere('employee_no', $empID);

                // Map the additional employee info
                $item['LName'] = $employeeInfo['last_name'] ?? null;
                $item['initial'] = $employeeInfo['initials'] ?? null;
                $item['title'] = $employeeInfo['title'] ?? null;
                $item['department'] = $employeeInfo['department_name'] ?? null;
                $item['status'] = $employeeInfo['employee_status_id'] ?? null;
                $item['email'] = $employeeInfo['email'] ?? null;
                $item['mobile_no'] = $employeeInfo['mobile_no'] ?? null;

                return $item;
            });
    }

    private function acdemicEmployeeData()
    {
        // Make API request to HRMS
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
            'x-api-key' => 'bf919ee9-09fa-43f5-94d6-3339888f0f5f',
        ])->post('https://hrms.sjp.ac.lk/api/emp/fgs/study/board/chair/person');

        // Decode the response to an associative array
        return json_decode($empDetails, true);
    }
}
