<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Http\Requests\CourseEditRequest;
use App\Http\Requests\CourseStoreRequest;
use App\Models\ChairPerson;
use App\Models\Course;
use App\Models\CourseCoordinator;
use App\Models\CourseFees;
use App\Models\CourseOperator;
use App\Models\StudyBoard;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http;

class CourseController extends Controller implements HasMiddleware
{
    public static function middleware(): array
    {
        return [
            'auth',
        ];
    }

    public function CourseIndex()
    {
        Gate::authorize('course.list');
        $empNo = Auth()->user()->reg_no;

        if (Auth()->user()->hasRole(['Super-Admin', 'Admin','Admin-Officer','FGS-Admin'])) {

            $data = Course::all();

        } elseif (Auth()->user()->hasRole(['Subject-Clerk','Subject-Assistant'])) {

            $data = Course::join('course_operators','courses.id','=','course_operators.course_code')
                   ->where('course_operators.emp_no', $empNo)
                   ->get();
        }


        return view('admin.setups.course.index', compact('data'));
    }

    public function CourseAdd()
    {
        Gate::authorize('course.create');
        $studyBoards = StudyBoard::where('active_status', 1)->get();
        $categories = $this->getCategories([3, 4, 5, 7]);
        $courseMediums = $categories->where('category_type_id', '3');
        $courseTypes = $categories->where('category_type_id', '4')->where('category_code', 0);
        $courseTypeExtensions = $categories->where('category_type_id', '5')->where('category_code', 0);
        $courseApplicationOpenMethods = $categories->where('category_type_id', '7');
        return view('admin.setups.course.add', compact('studyBoards', 'courseMediums', 'courseTypes', 'courseTypeExtensions', 'courseApplicationOpenMethods'));
    }

    public function CourseStore(CourseStoreRequest $request)
    {
        Gate::authorize('course.create');
        Course::create($request->prepareData());

        $notification = array(
            'message' => 'New Course Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('course.index')->with($notification);
    }

    public function CourseEdit($id)
    {
        Gate::authorize('course.updation');
        $courseId = decrypt($id);
        $editData = Course::find($courseId);
        $studyBoards = StudyBoard::where('active_status', 1)->get();
        $categories = $this->getCategories([3, 4, 6, 7]);
        $courseMediums = $categories->where('category_type_id', '3');
        $courseTypes = $categories->where('category_type_id', '4');
        $courseMainCategories = $categories->where('category_type_id', '6');
        $courseApplicationOpenMethods = $categories->where('category_type_id', '7');

        return view('admin.setups.course.edit', compact('editData', 'studyBoards', 'courseMediums', 'courseTypes', 'courseMainCategories', 'courseApplicationOpenMethods'));
    }

    public function CourseUpdate(CourseEditRequest $request, $id)
    {
        Gate::authorize('course.updation');
        $request->persist($id);

        $notification = array(
            'message' => 'Taught course data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('course.index')->with($notification);
    }

    public function CourseShow($id)
    {
        Gate::authorize('course.show');
        $courseId = decrypt($id);
        $data = Course::find($courseId);
        $courseCoordinator = CourseCoordinator::where('course_code', $courseId)->where('active_status', 1)->first();
        $courseFees = CourseFees::where('course_code', $courseId)->where('reg_year', date('Y'))->where('active_status', 1)->get();
        $courseOperators = CourseOperator::join('users', 'course_operators.emp_no', '=', 'users.reg_no')
                           ->select('course_operators.*', 'users.name as emp_name')
                           ->where('course_code', $courseId)
                           ->where('course_operators.active_status', 1)
                           ->get();

        $studyBoardChairPerson = ChairPerson::where('study_board_id', $data->study_board_id)->where('active_status', 1)->first();

        if($studyBoardChairPerson) {

            $studyBoardChairPersonDetails = $this->fetchEmployeeData($studyBoardChairPerson->emp_no);

        }else{

            $studyBoardChairPersonDetails = null;

        }


        if ($courseCoordinator) {

            $courseCoordinatorPersonDetails = $this->fetchEmployeeData($courseCoordinator->emp_no);

        } else {

            $courseCoordinatorPersonDetails = null;
        }

        return view('admin.setups.course.show', compact('data', 'courseCoordinator', 'courseFees', 'courseOperators', 'courseCoordinatorPersonDetails', 'studyBoardChairPersonDetails'));
    }


    public function CourseDelete($id)
    {
        Gate::authorize('course.delete');
        $courseId = decrypt($id);
        $data = Course::find($courseId);
        $data->delete();

        $courseCoordinator = CourseCoordinator::where('course_code', $courseId)->first();
        if ($courseCoordinator) {
            $courseCoordinator->delete();
        }

        $courseFees = CourseFees::where('course_code', $courseId)->get();
        if ($courseFees) {
            foreach ($courseFees as $courseFee) {
                $courseFee->delete();
            }
        }

        $courseOperators = CourseOperator::where('course_code', $courseId)->get();
        if ($courseOperators) {
            foreach ($courseOperators as $courseOperator) {
                $courseOperator->delete();
            }
        }

        $notification = array(
            'message' => 'Course Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('course.index')->with($notification);
    }

    public function CourseActive($id)
    {

        Gate::authorize('course.active');
        $courseId = decrypt($id);
        $data = Course::find($courseId);
        $data->active_status = 1;
        $data->updated_emp = Auth()->user()->reg_no;
        $data->updated_date = date('Y-m-d');
        $data->save();

        $notification = array(
            'message' => 'Course Activated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('course.index')->with($notification);
    }

    public function CourseInactive($id)
    {

        Gate::authorize('course.inactive');
        $courseId = decrypt($id);
        $data = Course::find($courseId);
        $data->active_status = 0;
        $data->updated_emp = Auth()->user()->reg_no;
        $data->updated_date = date('Y-m-d');
        $data->save();

        $notification = array(
            'message' => 'Course Inactivated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('course.index')->with($notification);
    }

    private function fetchEmployeeData($emp)
    {

        // Make API request to HRMS
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
            'x-api-key' => 'bf919ee9-09fa-43f5-94d6-3339888f0f5f',
        ])->post('https://hrms.sjp.ac.lk/api/emp/fgs/employee/data/get', ['employee_no' => $emp]);

        // Decode the response to an associative array
        return json_decode($empDetails, true);
    }
}
