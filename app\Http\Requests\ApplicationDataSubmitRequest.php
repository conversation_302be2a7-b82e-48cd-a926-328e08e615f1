<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\DocumentSummary;

class ApplicationDataSubmitRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [];

        if($this->has('submit')){

            $rules['title_id'] = 'required';
            $rules['initials'] = 'required|regex:/^([A-Z]\.)+$/';
            $rules['name_denoted_by_initials'] = 'required';
            $rules['last_name'] = 'required|alpha';
            $rules['dob'] = 'required|date_format:Y-m-d';
            $rules['gender_cat_id'] = 'required';
            $rules['civil_status_cat_id'] = 'required';
            $rules['permanent_address1'] = 'required';
            $rules['permanent_city'] = 'required';
            $rules['permanent_postal_code'] = 'required|max:5';
            $rules['postal_address1'] = 'required';
            $rules['postal_city'] = 'required';
            $rules['postal_postal_code'] = 'required';
            $rules['postal_postal_code'] = 'required|max:5';

            /******************************** */
            $rules['mobile_no'] = 'required';
            $rules['email'] = 'required|email';
            $rules['tel_home'] = 'nullable|numeric';
            $rules['tel_office'] = 'nullable|numeric';

            /***************************** */

            $rules['first_degree_name'] = 'required';
            $rules['first_degree_major_subject'] = 'required';
            $rules['first_degree_university'] = 'required';
            $rules['class_cat_id'] = 'required';
            $rules['first_degree_effective_date'] = 'required|date_format:Y-m-d';
            $rules['first_degree_duration'] = 'required|numeric';
            $rules['gpa'] = 'required';




            if($this->input('course_main_category') == 42) {

                $rules['proposal_title'] = 'required|string|max:500';
                $rules['research_summary'] = 'nullable|string';

                $rules['supervisor1_name'] = 'required|string|max:255';
                $rules['supervisor1_email'] = 'required|email|max:255';
                $rules['supervisor1_address'] = 'required|string|max:500';
                $rules['supervisor1_mobile'] = 'required|string|max:20';
                $rules['supervisor1_tel_home'] = 'nullable|string|max:20';
                $rules['supervisor1_tel_office'] = 'nullable|string|max:20';

                $rules['supervisor2_name'] = 'nullable|string|max:255';
                $rules['supervisor2_email'] = 'nullable|email|max:255';
                $rules['supervisor2_address'] = 'nullable|string|max:500';
                $rules['supervisor2_mobile'] = 'nullable|string|max:20';
                $rules['supervisor2_tel_home'] = 'nullable|string|max:20';
                $rules['supervisor2_tel_office'] = 'nullable|string|max:20';
            }

            $rules['referee1_name'] = 'required|string|max:255';
            $rules['referee1_email'] = 'required|email|max:255';
            $rules['referee1_phone'] = 'required|string|max:20';

            $rules['referee2_name'] = 'required|string|max:255';
            $rules['referee2_email'] = 'required|email|max:255';
            $rules['referee2_phone'] = 'required|string|max:20';



            // Document upload validation rules - check if files already exist
            $applicantId = $this->input('applicant_id');
            $existingDocs = DocumentSummary::where('applicant_id', $applicantId)->pluck('document_type')->toArray();

            // Profile photo - required if not already uploaded
            $rules['profile_photo'] = in_array(1, $existingDocs) ? 'nullable|file|mimes:jpg,jpeg,png|max:2048' : 'required|file|mimes:jpg,jpeg,png|max:2048';

            // Degree certificate - required if not already uploaded
            $rules['degree_certificate'] = in_array(2, $existingDocs) ? 'nullable|file|mimes:pdf|max:5120' : 'required|file|mimes:pdf|max:5120';

            // Degree transcript - required if not already uploaded
            $rules['degree_transcript'] = in_array(3, $existingDocs) ? 'nullable|file|mimes:pdf|max:5120' : 'required|file|mimes:pdf|max:5120';

            // Birth certificate - required if not already uploaded
            $rules['birth_certificate'] = in_array(4, $existingDocs) ? 'nullable|file|mimes:pdf|max:5120' : 'required|file|mimes:pdf|max:5120';

            // NIC/Passport - required if not already uploaded
            $rules['nic_passport_doc'] = in_array(5, $existingDocs) ? 'nullable|file|mimes:pdf|max:5120' : 'required|file|mimes:pdf|max:5120';

            if($this->input('course_main_category') == 42) {

                // Research proposal 1 - required if not already uploaded
                $rules['research_proposal1'] = in_array(6, $existingDocs) ? 'nullable|file|mimes:pdf|max:5120' : 'required|file|mimes:pdf|max:5120';

                // Research proposal 2 - always optional
                $rules['research_proposal2'] = 'nullable|file|mimes:pdf|max:5120';
            }

            $rules['terms'] = 'required|accepted';

        }else if($this->has('save')){

           $rules['initials'] = 'nullable|regex:/^([A-Z]\.)+$/';
           $rules['last_name'] = 'nullable|alpha';
           $rules['dob'] = 'nullable|date_format:Y-m-d';
           $rules['permanent_postal_code'] = 'nullable|max:5';
           $rules['postal_postal_code'] = 'nullable|max:5';
           $rules['tel_home'] = 'nullable|numeric';
           $rules['tel_office'] = 'nullable|numeric';

           $rules['duration'] = 'nullable|numeric';

           if($this->input('course_main_category') == 42) {

                $rules['proposal_title'] = 'nullable|string|max:500';
                $rules['research_summary'] = 'nullable|string';

                $rules['supervisor1_name'] = 'nullable|string|max:255';
                $rules['supervisor1_email'] = 'nullable|email|max:255';
                $rules['supervisor1_address'] = 'nullable|string|max:500';
                $rules['supervisor1_mobile'] = 'nullable|string|max:20';
                $rules['supervisor1_tel_home'] = 'nullable|string|max:20';
                $rules['supervisor1_tel_office'] = 'nullable|string|max:20';

                $rules['supervisor2_name'] = 'nullable|string|max:255';
                $rules['supervisor2_email'] = 'nullable|email|max:255';
                $rules['supervisor2_address'] = 'nullable|string|max:500';
                $rules['supervisor2_mobile'] = 'nullable|string|max:20';
                $rules['supervisor2_tel_home'] = 'nullable|string|max:20';
                $rules['supervisor2_tel_office'] = 'nullable|string|max:20';
            }

            $rules['referee1_name'] = 'nullable|string|max:255';
            $rules['referee1_email'] = 'nullable|email|max:255';
            $rules['referee1_phone'] = 'nullable|string|max:20';

            $rules['referee2_name'] = 'nullable|string|max:255';
            $rules['referee2_email'] = 'nullable|email|max:255';
            $rules['referee2_phone'] = 'nullable|string|max:20';

            // Document upload validation rules
            $rules['profile_photo'] = 'nullable|file|mimes:jpg,jpeg,png|max:2048';
            $rules['degree_certificate'] = 'nullable|file|mimes:pdf|max:5120';
            $rules['degree_transcript'] = 'nullable|file|mimes:pdf|max:5120';
            $rules['birth_certificate'] = 'nullable|file|mimes:pdf|max:5120';
            $rules['nic_passport_doc'] = 'nullable|file|mimes:pdf|max:5120';

            if($this->input('course_main_category') == 42) {
                $rules['research_proposal1'] = 'nullable|file|mimes:pdf|max:5120';
                $rules['research_proposal2'] = 'nullable|file|mimes:pdf|max:5120';
            }
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'title_id.required' => 'Please select your title',
            'initials.required' => 'Please enter your initials',
            'initials.regex' => 'Please enter your initials in the correct format',
            'name_denoted_by_initials.required' => 'Please select an option',
            'last_name.required' => 'Please enter your last name',
            'last_name.alpha' => 'Please enter a valid last name',
            'dob.required' => 'Please enter your date of birth',
            'dob.date_format' => 'Please enter a valid date of birth',
            'gender_cat_id.required' => 'Please select your gender',
            'civil_status_cat_id.required' => 'Please select your civil status',
            'permanent_address1.required' => 'Please enter your permanent address',
            'permanent_city.required' => 'Please enter your permanent city',
            'permanent_postal_code.required' => 'Please enter your permanent address postal code',
            'permanent_postal_code.max' => 'Please enter a valid postal code',
            'postal_address1.required' => 'Please enter your postal address',
            'postal_city.required' => 'Please enter your postal city',
            'postal_postal_code.required' => 'Please enter your postal address postal code',
            'postal_postal_code.max' => 'Please enter a valid postal code',
            'mobile_no.required' => 'Please enter your mobile number',
            'email.required' => 'Please enter your email address',
            'email.email' => 'Please enter a valid email address',
            'tel_home.numeric' => 'Please enter a valid home telephone number',
            'tel_office.numeric' => 'Please enter a valid office telephone number',
            'first_degree_name.required' => 'Please enter your first degree name',
            'first_degree_major_subject.required' => 'Please enter your major subject',
            'first_degree_university.required' => 'Please enter your university',
            'class_cat_id.required' => 'Please select your degree class',
            'first_degree_effective_date.required' => 'Please enter your effective date',
            'first_degree_effective_date.date_format' => 'Please enter a valid effective date',
            'first_degree_duration.required' => 'Please enter degree duration',
            'first_degree_duration.numeric' => 'Please enter a valid duration',
            'gpa.required' => 'Please enter your GPA',

            // Terms and conditions validation message
            'terms.required' => 'You must agree to the terms and conditions to submit your application',
            'terms.accepted' => 'You must agree to the terms and conditions to submit your application',

            // Research course validation messages
            'proposal_title.required' => 'Please enter your research proposal title',
            'proposal_title.max' => 'Research proposal title must not exceed 500 characters',
            'research_summary.string' => 'Please enter a valid research summary',

            // Document upload validation messages
            'profile_photo.required' => 'Please upload your profile photo',
            'profile_photo.mimes' => 'Profile photo must be in JPG, JPEG, or PNG format',
            'profile_photo.max' => 'Profile photo must not exceed 2MB',

            'degree_certificate.required' => 'Please upload your degree certificate',
            'degree_certificate.mimes' => 'Degree certificate must be in PDF format',
            'degree_certificate.max' => 'Degree certificate must not exceed 5MB',

            'degree_transcript.required' => 'Please upload your degree transcript',
            'degree_transcript.mimes' => 'Degree transcript must be in PDF format',
            'degree_transcript.max' => 'Degree transcript must not exceed 5MB',

            'birth_certificate.required' => 'Please upload your birth certificate',
            'birth_certificate.mimes' => 'Birth certificate must be in PDF format',
            'birth_certificate.max' => 'Birth certificate must not exceed 5MB',

            'nic_passport_doc.required' => 'Please upload your NIC/Passport document',
            'nic_passport_doc.mimes' => 'NIC/Passport document must be in PDF format',
            'nic_passport_doc.max' => 'NIC/Passport document must not exceed 5MB',

            'research_proposal1.required' => 'Please upload your research proposal',
            'research_proposal1.mimes' => 'Research proposal must be in PDF format',
            'research_proposal1.max' => 'Research proposal must not exceed 5MB',

            'research_proposal2.mimes' => 'Research proposal must be in PDF format',
            'research_proposal2.max' => 'Research proposal must not exceed 5MB',

            // First Supervisor validation messages
            'supervisor1_name.required' => 'Please enter first supervisor name',
            'supervisor1_name.max' => 'First supervisor name must not exceed 255 characters',
            'supervisor1_email.required' => 'Please enter first supervisor email',
            'supervisor1_email.email' => 'Please enter a valid first supervisor email',
            'supervisor1_email.max' => 'First supervisor email must not exceed 255 characters',
            'supervisor1_address.required' => 'Please enter first supervisor address',
            'supervisor1_address.max' => 'First supervisor address must not exceed 500 characters',
            'supervisor1_mobile.required' => 'Please enter first supervisor mobile number',
            'supervisor1_mobile.max' => 'First supervisor mobile number must not exceed 20 characters',
            'supervisor1_tel_home.max' => 'First supervisor home telephone must not exceed 20 characters',
            'supervisor1_tel_office.max' => 'First supervisor office telephone must not exceed 20 characters',

            // Second Supervisor validation messages
            'supervisor2_name.max' => 'Second supervisor name must not exceed 255 characters',
            'supervisor2_email.email' => 'Please enter a valid second supervisor email',
            'supervisor2_email.max' => 'Second supervisor email must not exceed 255 characters',
            'supervisor2_address.max' => 'Second supervisor address must not exceed 500 characters',
            'supervisor2_mobile.max' => 'Second supervisor mobile number must not exceed 20 characters',
            'supervisor2_tel_home.max' => 'Second supervisor home telephone must not exceed 20 characters',
            'supervisor2_tel_office.max' => 'Second supervisor office telephone must not exceed 20 characters',

            // Referee validation messages
            'referee1_name.required' => 'Please enter first referee name',
            'referee1_name.max' => 'First referee name must not exceed 255 characters',
            'referee1_email.required' => 'Please enter first referee email',
            'referee1_email.email' => 'Please enter a valid first referee email',
            'referee1_email.max' => 'First referee email must not exceed 255 characters',
            'referee1_phone.required' => 'Please enter first referee phone number',
            'referee1_phone.max' => 'First referee phone number must not exceed 20 characters',

            'referee2_name.required' => 'Please enter second referee name',
            'referee2_name.max' => 'Second referee name must not exceed 255 characters',
            'referee2_email.required' => 'Please enter second referee email',
            'referee2_email.email' => 'Please enter a valid second referee email',
            'referee2_email.max' => 'Second referee email must not exceed 255 characters',
            'referee2_phone.required' => 'Please enter second referee phone number',
            'referee2_phone.max' => 'Second referee phone number must not exceed 20 characters',

        ];
    }
}
