<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class Category2Seeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $datas =
            [
                ['category_type_id' => 9, 'category_name' => 'Ven.', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 9, 'category_name' => 'Rev.', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],
                ['category_type_id' => 9, 'category_name' => 'Mr.', 'display_name' => '','category_code'=> 3 ,'sorting_order' => 3],
                ['category_type_id' => 9, 'category_name' => 'Mrs.', 'display_name' => '','category_code'=> 4 ,'sorting_order' => 4],
                ['category_type_id' => 9, 'category_name' => 'Miss', 'display_name' => '','category_code'=> 4 ,'sorting_order' => 4],
                ['category_type_id' => 9, 'category_name' => 'Ms.', 'display_name' => '','category_code'=> 4 ,'sorting_order' => 4],

                ['category_type_id' => 10, 'category_name' => 'Male', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 10, 'category_name' => 'Female', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],

                ['category_type_id' => 11, 'category_name' => 'Active', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 11, 'category_name' => 'Pass Out', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],
                ['category_type_id' => 11, 'category_name' => 'Cancel', 'display_name' => '','category_code'=> 3 ,'sorting_order' => 3],

            ];

            foreach($datas as $data){
                DB::table('categories')->insert($data);
            }
    }
}
