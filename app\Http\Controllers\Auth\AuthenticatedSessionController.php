<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Mail\LoginNotificationMail;
use Carbon\Carbon;
use GeoIp2\Database\Reader;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\View\View;
use Jenssegers\Agent\Agent;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.admin_login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        $agent = new Agent();
        $currentTime = Carbon::now();
        $ipAddress = App::environment('local')
        ? $request->getClientIp()
        : Http::get('https://api.ipify.org')->body();

        // Create a unique device fingerprint using multiple identifiers
        $deviceFingerprint = hash('sha256', implode('|', [
            $agent->device(),
            $agent->platform(),
            $agent->browser(),
            $agent->version($agent->browser()),
            $request->header('User-Agent'),
            $request->header('Accept-Language'),
            $request->header('Accept-Encoding'),
            $request->server('HTTP_SEC_CH_UA_PLATFORM'),
            $request->server('HTTP_SEC_CH_UA'),
        ]));

        $user = $request->user();
        $lastDevice = $user->last_device;

        $user->update([
            'last_login_at' => $currentTime->toDateTimeString(),
            'last_login_ip' => $ipAddress,
            'last_device' => $deviceFingerprint,
        ]);

        // Get geolocation data
        $location = 'Unknown Location';
        try {
            $reader = new Reader(storage_path('app/GeoLite2-City.mmdb'));
            $record = $reader->city($ipAddress);
            $location = implode(', ', array_filter([
                $record->city->name,
                $record->mostSpecificSubdivision->name,
                $record->country->name
            ]));
        } catch (\Exception $e) {
            // Fallback to default location if geolocation fails
            $location = '-';
        }

        // Send login notification email only if device is different
        if (!$lastDevice || $lastDevice !== $deviceFingerprint) {

            $loginInfo = [
                'ip' => $ipAddress,
                'device' => $deviceFingerprint,
                'browser' => $agent->browser(),
                'time' => $currentTime->format('Y-m-d H:i:s'),
                'location' => $location
            ];

            Mail::to($user->email)->queue(new LoginNotificationMail($loginInfo));
        }

        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
