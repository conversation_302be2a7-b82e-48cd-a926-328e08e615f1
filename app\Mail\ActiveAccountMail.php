<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ActiveAccountMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;


    public function __construct()
    {
        //
    }


    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Account Activation Notification',
        );
    }


    public function content(): Content
    {
        return new Content(
            markdown: 'emails.active-account',
        );
    }


    public function attachments(): array
    {
        return [];
    }
}
