<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CourseFeeStoreRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }


    public function rules()
    {
        return [
            'course_code' => 'required|string|max:255',

            'reg_year' => 'required|array',
            'reg_year.*' => 'required|integer',

            'batch' => 'required|array',
            'batch.*' => 'required|integer',

            'intake' => 'required|array',
            'intake.*' => 'required|integer',

            'pay_income_type_id' => 'required|array',
            'pay_income_type_id.*' => 'required|integer',

            'amount' => 'required|array',
            'amount.*' => 'required|numeric|min:0',

            // Ensure all arrays have the same number of elements
            // 'reg_year' => 'required|array|size:' . count(request('batch')),
            // 'batch' => 'required|array|size:' . count(request('intake')),
            // 'intake' => 'required|array|size:' . count(request('pay_income_type_id')),
            // 'pay_income_type_id' => 'required|array|size:' . count(request('amount')),
        ];
    }
}
