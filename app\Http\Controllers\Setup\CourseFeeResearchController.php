<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Http\Requests\CourseResearchFeeStoreRequest;
use App\Models\Category;
use App\Models\Course;
use App\Models\CourseResearchFee;
use App\Models\IncomeType;
use Carbon\Carbon;
use Illuminate\Support\Facades\Gate;

class CourseFeeResearchController extends Controller
{
    public static function middleware(): array
    {
        return [
            'auth',
        ];
    }

    public function CourseResearchFeeIndex()
    {
        Gate::authorize('course.research.fee.list');
        $empNo = Auth()->user()->reg_no;

        if (Auth()->user()->hasRole(['Super-Admin', 'Admin','Admin-Officer','FGS-Admin'])) {

            $data = Course::where('active_status', 1)->where('course_main_category',42)->get();

        } elseif (Auth()->user()->hasRole(['Subject-Clerk','Subject-Assistant'])) {

            $data = Course::join('course_operators','courses.id','=','course_operators.course_code')
                    ->where('course_operators.emp_no', $empNo)
                    ->where('active_status', 1)
                    ->where('course_main_category',42)->get();

        }

        return view('admin.setups.course_research_fee.index', compact('data'));
    }

    public function CourseResearchFeeShow($id)
    {
        Gate::authorize('course.research.fee.show');
        $courseId = decrypt($id);
        $courseResearchFees = CourseResearchFee::where('course_code', $courseId)->where('active_status', 1)->orderBy('reg_year', 'desc')->orderBy('batch', 'desc')->orderBy('research_payment_type_id')->get();
        $researchPaymentTypes = Category::where('category_type_id', 17)->get();
        $incomeTypes = IncomeType::all();
        $data = Course::find($courseId);

        return view('admin.setups.course_research_fee.show', compact('data', 'courseResearchFees', 'researchPaymentTypes', 'incomeTypes'));
    }

    public function CourseResearchFeeAllShow($id)
    {
        Gate::authorize('course.research.fee.all.show');
        $courseId = decrypt($id);
        $courseResearchFees = CourseResearchFee::where('course_code', $courseId)->where('active_status', 1)->orderBy('reg_year', 'desc')->orderBy('batch', 'desc')->orderBy('research_payment_type_id')->get();
        $data = Course::find($courseId);

        return view('admin.setups.course_research_fee.show_all', compact('data', 'courseResearchFees'));
    }

    public function CourseResearchFeeStore(CourseResearchFeeStoreRequest $request)
    {
        Gate::authorize('course.research.fee.create');

        $data = $request->all();

        if (count($data['reg_year']) > 0) {
            foreach ($data['reg_year'] as $index => $reg_year) {

                $existingRecord = CourseResearchFee::where([
                    'course_code' => $data['course_code'],
                    'reg_year' => $data['reg_year'][$index],
                    'batch' => $data['batch'][$index],
                    'intake' => $data['intake'][$index],
                    'pay_income_type_id' => $data['pay_income_type_id'][$index],
                    'research_payment_type_id' => $data['research_payment_type_id'][$index],
                    'active_status' => 1

                ])->first();

                if ($existingRecord) {
                    $existingRecord->update([
                        'updated_emp' => Auth()->user()->reg_no,
                        'updated_date' => Carbon::now(),
                        'active_status' => 0
                    ]);
                }

                CourseResearchFee::create([
                    'course_code' => $data['course_code'],
                    'reg_year' => $data['reg_year'][$index],
                    'batch' => $data['batch'][$index],
                    'intake' => $data['intake'][$index],
                    'pay_income_type_id' => $data['pay_income_type_id'][$index],
                    'research_payment_type_id' => $data['research_payment_type_id'][$index],
                    'amount' => $data['amount'][$index],
                    'active_status' => 1,
                    'created_emp' => Auth()->user()->reg_no,
                    'created_date' => Carbon::now(),
                ]);
            }
        } else {
            $notification = [
                'message' => 'Please add at least one research course fee record',
                'alert-type' => 'error'
            ];

            return redirect()->route('course.research.fee.show', encrypt($data['course_code']))->with($notification);
        }

        $notification = [
            'message' => 'New Research Course Fee Inserted or Updated Successfully',
            'alert-type' => 'success'
        ];

        return redirect()->route('course.research.fee.show', encrypt($data['course_code']))->with($notification);
    }

    public function CourseResearchFeeDelete($id)
    {
        Gate::authorize('course.research.fee.delete');
        $courseResearchFeeId = decrypt($id);
        $data = CourseResearchFee::find($courseResearchFeeId);
        $data->active_status = 0;
        $data->updated_emp = Auth()->user()->reg_no;
        $data->updated_date = Carbon::now();
        $data->save();

        $notification = array(
            'message' => 'Research Course Fee Data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('course.research.fee.show', encrypt($data->course_code))->with($notification);
    }
}
