<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applicants', function (Blueprint $table) {
            $table->string('permanent_city_id')->nullable()->change();
            $table->string('postal_city_id')->nullable()->change();
            $table->string('permanent_postal_code')->nullable()->after('permanent_city_id');
            $table->string('postal_postal_code')->nullable()->after('postal_city_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applicants', function (Blueprint $table) {
             $table->integer('permanent_city_id')->nullable()->change();
             $table->integer('postal_city_id')->nullable()->change();
             $table->dropColumn('permanent_postal_code');
             $table->dropColumn('postal_postal_code');
        });
    }
};
