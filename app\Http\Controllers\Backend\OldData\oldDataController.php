<?php

namespace App\Http\Controllers\Backend\OldData;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\checkPayment;
use App\Models\Course;
use App\Models\firstSupervisor;
use App\Models\IncomeType;
use App\Models\otherSupervisor;
use App\Models\RegisteredStudent;
use App\Models\researchTitle;
use App\Models\studentDataOld;
use App\Models\StudyBoardSubject;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class oldDataController extends Controller
{
   public function oldDataListOpen(Request $request)
   {
      if (Auth()->user()->hasRole(['Super-Admin', 'Admin', 'FGS-Admin', 'Admin-Officer'])) {
         $course_list = Course::where('course_main_category', 41)->get();
      } else {
         $course_list = Course::join('course_operators', 'course_operators.course_code', '=', 'courses.id')
            ->select('courses.previous_course_code', 'courses.course_name')
            ->where('courses.course_main_category', 41)
            ->where('course_operators.emp_no', Auth()->user()->reg_no)
            ->get();
      }



      if (isset($request->course)) {
         if ($request->course == 0) {
            $data_text = studentDataOld::select(
               'student_data_olds.reg_year',
               'student_data_olds.reg_no',
               'student_data_olds.course_code',
               'student_data_olds.title',
               'student_data_olds.initials',
               'student_data_olds.LName',
               'student_data_olds.id'
            )
               ->where('status', 0)
               ->where('research_status', 2)
               ->orderBy('reg_year')
               ->orderBy('reg_no')
               ->get();
         } else {
            $data_text = studentDataOld::select(
               'student_data_olds.reg_year',
               'student_data_olds.reg_no',
               'student_data_olds.course_code',
               'student_data_olds.title',
               'student_data_olds.initials',
               'student_data_olds.LName',
               'student_data_olds.id'
            )
               ->where('status', 0)
               ->where('course_code', $request->course)
               ->orderBy('reg_year')
               ->orderBy('reg_no')
               ->get();
         }
      } else {
         $data_text = array();
      }

      return view('admin.student.oldData.oldStudentList', compact('course_list', 'data_text'));
   }

   public function oldStudentDetails($id)
   {

      $row_id = decrypt($id);
      $data_text = studentDataOld::select(
         'student_data_olds.*',
      )
         ->where('student_data_olds.id', $row_id)
         ->get();

      if (count($data_text) > 0) {
         foreach ($data_text as $data_texts) {

            $old_course_code = $data_texts->course_code;
            $course_type = $data_texts->research_status;
            $sb = $data_texts->sb_id;
            $reg_year = $data_texts->reg_year;
            $reg_no = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->reg_no);
            $reg_date = Carbon::createFromFormat('d/m/Y', $data_texts->reg_date)->format('Y-m-d');
            $nic = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->stud_nic);
            $index_no = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->index_no);
            $title = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->title);
            $initials = ucwords(strtoupper(preg_replace('/[^a-zA-Z\s]/', '', strtok($data_texts->initials, ' '))));
            $LName = ucwords(strtolower(preg_replace('/[^a-zA-Z\s]/', '', $data_texts->LName)));
            $FName = ucwords(strtolower(preg_replace('/[^a-zA-Z\s]/', '', $data_texts->FName)));
            $gender = $data_texts->gender;

            if ($data_texts->bDate === null) {
               $bDate = today()->format('Y-m-d');
            } else {
               if (Carbon::hasFormat($data_texts->bDate, 'm/d/Y')) {
                  $bDate = Carbon::createFromFormat('m/d/Y', $data_texts->bDate)->format('Y-m-d');
               } else {

                  $bDate = today()->format('Y-m-d');
               }
            }

            $email = preg_replace('/[^A-Za-z0-9\s\/.,@-]/', '', $data_texts->email);
            $profession = preg_replace('/[^a-zA-Z\s]/', '', $data_texts->profession);
            $mobile = preg_replace('/\D/', '', $data_texts->mobile);
            $hometp = preg_replace('/\D/', '', $data_texts->home_tp);
            $othertp = preg_replace('/\D/', '', $data_texts->other_tp);
            $per_add_line1 = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->permanent_add_line1);
            $per_add_line2 = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->permanent_add_line2);
            $per_add_line3 = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->permanent_add_line3);
            $per_add_city = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->permanent_city);
            $post_add_line1 = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->postal_add_line1);
            $post_add_line2 = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->postal_add_line2);
            $post_add_line3 = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->postal_add_line3);
            $post_add_city = preg_replace('/[^A-Za-z0-9\s\/.,-]/', '', $data_texts->postal_city);
         }

         if ($course_type == 2) {
            $course_list = Course::where('course_main_category', 42)
               ->where('study_board_id', $sb)
               ->get();

            $sub_list = StudyBoardSubject::where('study_board_id', $sb)->get();

            $course_code = '';
            $course_name = '';
            $pay_course_id = '';

            $response = Http::asForm()->post('http://************/API/APIpaymentForNIC.php', [
               'RegYear' => $reg_year,
               'NIC' => $nic,
               'key' => 'e2a5ee4a4f9dc023bdba233d9a8f1f8a'

            ]);
            if ($response->successful()) {
               $responseData = $response->json();

               $ref_no = $responseData['0']['nStudentId'] ?? null;
               //dd($ref_no);

            } else {
               $ref_no = '1234';
            }
         } else {
            $course_list = [];
            $sub_list = [];
            $course_text = Course::where('previous_course_code', $old_course_code)->get();
            if (count($course_text) > 0) {
               foreach ($course_text as $course_texts) {
                  $course_code = $course_texts->id;
                  $course_name = $course_texts->course_name;
                  $pay_course_id = $course_texts->payment_course_code;
               }
            }

            $response = Http::asForm()->post('http://************/API/API_studentAccId_old.php', [
               'regYear' => $reg_year,
               'courseCode' => $pay_course_id,
               'NIC' => $nic,
               'key' => '977f2bf83fd0e527b77d9155390ff66d'

            ]);

            if ($response->successful()) {
               $responseData = $response->json();
               $nStudentId = $responseData['nStudentId'] ?? null;

               if ($nStudentId) {
                  $ref_no = $nStudentId;
               } else {
                  $ref_no = '';
               }
            } else {
               $ref_no = '';
            }
         }
         if ($ref_no != '') {
            # code...


            $response2 = Http::asForm()->post('http://************/API/APIpaymentsForId.php', [
               'StudentId' => $ref_no,
               'key' => 'e2a5ee4a4f9dc023bdba233d9a8f1f8a'

            ]);

            if ($response2->successful()) {
               $dataPay = json_decode($response2->body(), true);

               $maxPayId = collect($dataPay)->max('nPayId');

               $pay_data = collect($dataPay)->map(function ($item) {
                  // Fetch the income type from the database using the nIncomeType value
                  $incomeType = IncomeType::where('pay_income_type_code', $item['nIncomeType'])->first();

                  // Prepare the result array for each item in dataPay
                  return [
                     'income_type' => $incomeType ? $incomeType->pay_income_type_code : null, // Income type
                     'income_text' => $incomeType ? $incomeType->income_type_name : null, // Income type text
                     'amount' => $item['nAmount'], // Amount
                     'payDate' => $item['dPayDate'], // Payment Date
                  ];
               })->toArray();
            } else {
               $pay_data = [];
               $maxPayId = 0;
            }
         } else {
            $pay_data = [];
            $maxPayId = 0;
         }
      } else {
         $old_course_code = '';
         $course_code = '';
         $course_name = '';
         $reg_year = '';
         $reg_no = '';
         $reg_date = '';
         $nic = '';
         $index_no = '';
         $title = '';
         $initials = '';
         $LName = '';
         $FName = '';
         $gender = '';
         $bDate = today();
         $email = '';
         $profession = '';
         $mobile = '';
         $hometp = '';
         $othertp = '';
         $per_add_line1 = '';
         $per_add_line2 = '';
         $per_add_line3 = '';
         $per_add_city = '';
         $post_add_line1 = '';
         $post_add_line2 = '';
         $post_add_line3 = '';
         $post_add_city = '';
         $ref_no = '';
         $pay_data = [];
         $course_list = [];
         $sub_list = [];
         $course_type = 1;
         $maxPayId = 0;
      }

      $title_text = Category::where('category_type_id', 9)->get();

      $gender_text = Category::where('category_type_id', 10)->get();

      $status_text = Category::where('category_type_id', 11)->get();

      $supEmpDetails = Http::withHeaders([
         'Content-Type' => 'application/json',
         'x-api-key' => 'bf919ee9-09fa-43f5-94d6-3339888f0f5f',
      ])->post('https://hrms.sjp.ac.lk/api/emp/fgs/study/board/chair/person');


      $sup_data = json_decode($supEmpDetails, true);


      return view('admin.student.oldData.oldStudentDetails', compact(
         'old_course_code',
         'course_code',
         'course_name',
         'reg_year',
         'reg_no',
         'reg_date',
         'nic',
         'index_no',
         'title',
         'initials',
         'LName',
         'FName',
         'gender',
         'bDate',
         'email',
         'profession',
         'mobile',
         'hometp',
         'othertp',
         'per_add_line1',
         'per_add_line2',
         'per_add_line3',
         'per_add_city',
         'post_add_line1',
         'post_add_line2',
         'post_add_line3',
         'post_add_city',
         'ref_no',
         'pay_data',
         'title_text',
         'gender_text',
         'status_text',
         'row_id',
         'course_list',
         'sub_list',
         'course_type',
         'maxPayId',
         'sup_data'
      ));
   }

   public function oldDataStore(Request $request)
   {

      $rules = [
         'reg_year' => 'required',
         'reg_no' => 'required',
         'reg_date' => 'required',
         'nic' => 'required|min:10|max:12',
         'title' => 'required|numeric|gt:0',
         'initials' => 'required',
         'LName' => 'required',
         'FName' => 'required',
         'gender' => 'required|numeric|gt:0',
         'bDate' => 'required',
         'mobile' => 'required',
         'hometp' => 'required',
         'post_add_line1' => 'required',
         'post_add_line2' => 'required',
         'post_add_city' => 'required',
         'per_add_line1' => 'required',
         'per_add_line2' => 'required',
         'per_add_city' => 'required',
         'active' => 'required|numeric|gt:0',
         'row_id' => 'required',
         'course_type' => 'required|numeric'
      ];


      if ($request->course_type != 2) {
         $rules['course_code'] = 'required';
         $rules['group'] = 'required';
      }


      if ($request->course_type == 2) {
         $rules['research_title'] = 'required|string';
         $rules['sup1Name'] = 'required';
         $rules['courseR'] = 'required';
         $rules['subject'] = 'required';
      }

      $validated = $request->validate($rules);


      if (!empty($request->checkAmount) && is_array($request->checkAmount) && count(array_filter($request->checkAmount)) > 0) {
         $check_available = 1;
      } else {
         $check_available = 0;
      }

      $reg_row_count = RegisteredStudent::where('old_data', 1)->count();
      $reg_no = 10000 + $reg_row_count;

      $save_text = new RegisteredStudent();
      if ($request->course_type == 2) {
         $save_text->course_code = $request->courseR;
         $save_text->subject_code = $request->subject;
      } else {
         $save_text->course_code = $request->course_code;
         $save_text->group = $request->group;
         $save_text->intake = $request->intake;
      }

      $save_text->registered_year = $request->reg_year;
      $save_text->Reg_no = $reg_no;
      $save_text->reg_no_old = $request->reg_no;
      $save_text->reg_no_report = $request->reg_no;
      $save_text->index_no = $request->index_no;
      $save_text->reg_date = $request->reg_date;
      $save_text->payment_ref_no = $request->pay_refNo;
      $save_text->check_pay_available = $check_available;
      $save_text->old_nic = $request->nic;
      $save_text->new_nic = $request->nic;
      $save_text->active_nic = $request->nic;
      $save_text->title_id = $request->title;
      $save_text->initials = $request->initials;
      $save_text->LName = $request->LName;
      $save_text->FNames = $request->FName;
      $save_text->gender_id = $request->gender;
      $save_text->bDate = $request->bDate;
      $save_text->email = $request->email;
      $save_text->sjp_email = $request->reg_no . '@sjp.ac.lk';
      $save_text->profession = $request->profession;
      $save_text->mobile = $request->mobile;
      $save_text->home_tp = $request->hometp;
      $save_text->other_tp = $request->othertp;
      $save_text->postal_add_line1 = $request->post_add_line1;
      $save_text->postal_add_line2 = $request->post_add_line2;
      $save_text->postal_add_line3 = $request->post_add_line3;
      $save_text->postal_add_city = $request->post_add_city;
      $save_text->permanent_add_line1 = $request->per_add_line1;
      $save_text->permanent_add_line2 = $request->per_add_line2;
      $save_text->permanent_add_line3 = $request->per_add_line3;
      $save_text->permanent_add_city = $request->per_add_city;
      if ($request->active == 57) {
         $save_text->convocation_date = $request->convo_date;
         $save_text->convocation_no = $request->convoNo;
      }
      if ($request->course_type == 2) {
         $save_text->current_year = $request->currentYear;
      }
      $save_text->active_status = $request->active;
      $save_text->old_data = 1;
      $save_text->enter_user =  auth()->user()->reg_no;
      $save_text->enter_date = today();
      $save_text->save();

      $update_text = studentDataOld::where('id', $request->row_id)->first();
      $update_text->status = 1;
      $update_text->timestamps = false;
      $update_text->save();

      for ($i = 0; $i < count($request->checkAmount); $i++) {
         $check_text = new checkPayment();
         $check_text->reg_no = $reg_no;
         $check_text->payment_date = $request->checkDate[$i];
         $check_text->check_no = $request->checkNum[$i];
         $check_text->amount = $request->checkAmount[$i];
         $check_text->enter_user =  auth()->user()->reg_no;
         $check_text->enter_date = today();
         $check_text->save();
      }

      if ($request->course_type == 2) {

         if (trim($request->research_title) != '') {
            $research_text = new researchTitle();
            $research_text->reg_no = $reg_no;
            $research_text->research_title_text = $request->research_title;
            $research_text->enter_user =  auth()->user()->reg_no;
            $research_text->enter_date = today();
            $research_text->save();
         }

         $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'x-api-key' => 'bf919ee9-09fa-43f5-94d6-3339888f0f5f',
         ])->post('https://hrms.sjp.ac.lk/api/emp/fgs/employee/dep/fac', ['empNo' => $request->sup1Name]);


         if ($response->successful()) {
            $sup_data = $response->json();

            $dep_id = $sup_data['department_id'] ?? null;
            $dep_name = $sup_data['department_name'] ?? null;
            $fac_id = $sup_data['faculty_id'] ?? null;
         } else {
           
            Log::error('Failed to fetch supervisor department data', [
               'status' => $response->status(),
               'body' => $response->body(),
            ]);
           
         }

         $sup1 = new firstSupervisor();
         $sup1->reg_no = $reg_no;
         $sup1->emp_no = $request->sup1Name;
         $sup1->dep_id = $dep_id;
         $sup1->dep_name = $dep_name;
         $sup1->added_by = auth()->user()->reg_no;
         $sup1->added_date = today();
         $sup1->save();

         if ($request->sup_title > 0 && $request->sup_initials != '') {
            $other_sup = new otherSupervisor();
            $other_sup->reg_no = $reg_no;
            $other_sup->title = $request->sup_title;
            $other_sup->initial = strtoupper(preg_replace('/[^a-zA-Z]/', '', $request->sup_initials));
            $other_sup->last_name = ucwords(strtolower($request->sup_LName));
            $other_sup->email = $request->sup_email;
            $other_sup->mobile = $request->sup_mobile;
            $other_sup->address_line1 = $request->sup_post_add_line1;
            $other_sup->address_line2 = $request->sup_post_add_line2;
            $other_sup->address_line3 = $request->sup_post_add_line2;
            $other_sup->address_city = $request->sup_post_add_city;
            $other_sup->added_by = auth()->user()->reg_no;
            $other_sup->added_date = today();
            $other_sup->save();
         }
      }

      $notification = array(
         'message' => 'Successfully submitted.',
         'alert-type' => 'success'
      );

      return redirect()->route('student.old.data.list.open', ['course' => $request->course_code_old])->with($notification);
   }
}
