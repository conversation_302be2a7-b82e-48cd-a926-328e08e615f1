<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DocumentSummary extends Model
{
    protected $guarded = [];

    protected $fillable = [
        'applicant_id',
        'document_type',
        'document_category_id',
        'file_path',
        'original_file_name'
    ];

    /**
     * Document type constants for reference
     */
    const DOCUMENT_TYPES = [
        1 => 'Profile Photo',
        2 => 'Degree Certificate',
        3 => 'Degree Transcript',
        4 => 'Birth Certificate',
        5 => 'NIC/Passport',
        6 => 'Research Proposal 1',
        7 => 'Research Proposal 2',
    ];

    public function applicant()
    {
        return $this->belongsTo(Applicant::class);
    }

    /**
     * Get document type name
     */
    public function getDocumentTypeName()
    {
        return self::DOCUMENT_TYPES[$this->document_type] ?? 'Unknown';
    }

    /**
     * Get secure view URL for the document
     */
    public function getSecureViewUrl()
    {
        $token = encrypt($this->id);
        return route('application.document.view', ['token' => $token]);
    }
}
