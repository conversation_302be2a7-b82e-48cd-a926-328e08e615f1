<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\CategoryType;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RoleSeeder::class,
            UserSeeder::class,
            CategoryTypeSeeder::class,
            CategoryType2Seeder::class,
            CategoryType3Seeder::class,
            CategoryType4Seeder::class,
            CategorySeeder::class,
            Category2Seeder::class,
            Category3Seeder::class,
            Category4Seeder::class,
            PermissionSeeder::class,
            PermissionRoleSeeder::class,
            StudyBoardSeeder::class,
            StudyBoardSubjectSeeder::class,
            //ChairPeopleSeeder::class,
            IncomeTypeSeeder::class,
            CourseSeeder::class,
        ]);
    }
}
