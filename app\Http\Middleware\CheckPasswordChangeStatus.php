<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckPasswordChangeStatus
{
    protected $publicRoutes = [
        'login',
        'logout',
        'change.password.view',
        'change.password.update'
    ];

    public function handle(Request $request, Closure $next)
    {
        $route = $request->route();
        $middlewares = $route->gatherMiddleware();

        // Only check password change status if route has auth middleware
        if (!in_array('auth', $middlewares)) {
            return $next($request);
        }

        $currentRoute = $route->getName();

        if (in_array($currentRoute, $this->publicRoutes)) {
            return $next($request);
        }

        if (!Auth::check()) {
            return redirect()->route('login');
        }

        if (Auth::user()->password_change_status == 0) {
            return redirect()->route('change.password.view')->with([
                'message' => 'Please change your password before starting your session',
                'alert-type' => 'info'
            ]);
        }

        return $next($request);
    }
}