<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class LoginNotificationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $loginInfo;

    public function __construct($loginInfo)
    {
        $this->loginInfo = $loginInfo;
    }

    public function build()
    {
        return $this->subject('New Login Detected')
                    ->view('emails.login-notification')
                    ->with([
                        'ip' => $this->loginInfo['ip'],
                        'device' => $this->loginInfo['device'],
                        'browser' => $this->loginInfo['browser'],
                        'time' => $this->loginInfo['time'],
                        'location' => $this->loginInfo['location'] ?? 'Unknown'
                    ]);
    }
}
