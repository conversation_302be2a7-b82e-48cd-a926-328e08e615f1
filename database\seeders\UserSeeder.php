<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::create([
            'name' => 'Sachinda',
            'reg_no' => 12393,
            'email' => '<EMAIL>',
            'status' => 1,
            'email_verified_at' => now(),
            'password' => bcrypt('123'),//password

        ])->assignRole('Super-Admin');

        $user = User::create([
            'name' => 'Ranu<PERSON>',
            'reg_no' => 11301,
            'email' => '<EMAIL>',
            'status' => 1,
            'email_verified_at' => now(),
            'password' => bcrypt('123'),//password

        ])->assignRole('Admin');

        $user = User::create([
            'name' => 'Ashela',
            'reg_no' => 4654,
            'email' => '<EMAIL>',
            'status' => 1,
            'email_verified_at' => now(),
            'password' => bcrypt('123'),//password

        ])->assignRole('Admin');
    }
}
