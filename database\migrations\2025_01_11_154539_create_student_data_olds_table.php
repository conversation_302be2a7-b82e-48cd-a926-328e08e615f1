<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_data_olds', function (Blueprint $table) {
            $table->id();
            $table->string('reg_no')->nullable();
            $table->string('stud_nic')->nullable();
            $table->string('title')->nullable();
            $table->text('initials')->nullable();
            $table->text('LName')->nullable();
            $table->text('FName')->nullable();
            $table->string('gender')->nullable();
            $table->string('bDate')->nullable();
            $table->string('email')->nullable();
            $table->integer('course_code')->nullable();
            $table->integer('reg_year')->nullable();
            $table->integer('sb_id')->nullable();
            $table->string('reg_date')->nullable();
            $table->string('index_no')->nullable();
            $table->text('profession')->nullable();
            $table->string('mobile')->nullable();
            $table->string('home_tp')->nullable();
            $table->string('other_tp')->nullable();
            $table->text('postal_add_line1')->nullable();
            $table->text('postal_add_line2')->nullable();
            $table->text('postal_add_line3')->nullable();
            $table->text('postal_city')->nullable();
            $table->text('permanent_add_line1')->nullable();
            $table->text('permanent_add_line2')->nullable();
            $table->text('permanent_add_line3')->nullable();
            $table->text('permanent_city')->nullable();
            $table->integer('research_status')->nullable();
            $table->integer('status')->default(0);
            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_data_olds');
    }
};
