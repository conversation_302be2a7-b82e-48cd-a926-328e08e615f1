<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('registered_students', function (Blueprint $table) {
            $table->string('reg_no_report')->nullable()->after('reg_no_old');
            $table->integer('old_data')->default(0)->after('active_status');
            $table->string('active_nic')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('registered_students', function (Blueprint $table) {
            $table->dropColumn('reg_no_report');
            $table->dropColumn('old_data');             
            $table->integer('active_nic')->change();
        });
    }
};
