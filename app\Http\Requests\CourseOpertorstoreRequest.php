<?php

namespace App\Http\Requests;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class CourseOpertorstoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'emp_no' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'emp_no.required' => 'Select relavent course opertor',
        ];
    }

    public function prepareData()
    {

        $empParts = explode('|', $this->emp_no);

        $regNo = $empParts[0];
        $roleId = $empParts[1];

        // Determine operator_type based on roleId
        if ($roleId == 8) {
            $operatorType = 45;
        } elseif ($roleId == 9) {
            $operatorType = 46;
        }

        return [
            'course_code'    => $this->course_code,
            'emp_no'         => $regNo,
            'operator_type'  => $operatorType,
            'start_date'     => date('Y-m-d'),
            'active_status'  => 1,
            'created_emp'    => $this->user()->reg_no,
            'created_date'   => Carbon::now(),
        ];
    }
}
