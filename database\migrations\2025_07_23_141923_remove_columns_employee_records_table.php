<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employee_records', function (Blueprint $table) {
            $table->dropColumn('postal_add2');
            $table->dropColumn('postal_add3');
            $table->dropColumn('postal_city_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employee_records', function (Blueprint $table) {
            $table->string('postal_add2')->nullable()->after('postal_add1');
            $table->string('postal_add3')->nullable()->after('postal_add2');
            $table->integer('postal_city_id')->default(0)->after('postal_add3');
        });
    }
};
