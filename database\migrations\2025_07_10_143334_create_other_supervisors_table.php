<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('other_supervisors', function (Blueprint $table) {
            $table->id();
            $table->integer('reg_no');
            $table->integer('title');
            $table->string('initial');
            $table->string('last_name');
            $table->string('email')->nullable();
            $table->string('mobile')->nullable();
            $table->string('address_line1')->nullable();
            $table->string('address_line2')->nullable();
            $table->string('address_line3')->nullable();
            $table->string('address_city')->nullable();
            $table->integer('added_by');
            $table->date('added_date')->nullable();
            $table->integer('removed_by')->default(0);
            $table->date('removed_date')->nullable();
            $table->text('remove_reason')->nullable();            
            $table->integer('status')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('other_supervisors');
    }
};
