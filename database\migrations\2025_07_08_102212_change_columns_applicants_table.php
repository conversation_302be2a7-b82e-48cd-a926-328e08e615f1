<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applicants', function (Blueprint $table) {
            $table->string('old_nic')->nullable()->change();
            $table->string('new_nic')->nullable()->change();
            $table->string('dob')->nullable()->change();
            $table->string('passport')->nullable()->after('old_nic');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applicants', function (Blueprint $table) {
            $table->string('old_nic')->nullable(false)->change();
            $table->string('new_nic')->nullable(false)->change();
            $table->string('dob')->nullable(false)->change();
            $table->dropColumn('passport');
        });
    }
};
