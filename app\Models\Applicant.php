<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Applicant extends Model
{
    use HasFactory;

    protected $guarded = [];

    /**
     * Get the qualifications for the applicant.
     */
    public function qualifications()
    {
        return $this->hasMany(Qualification::class);
    }

    /**
     * Get the documents for the applicant.
     */
    public function documents()
    {
        return $this->hasMany(DocumentSummary::class);
    }

    public function getTitleName()
    {
        return $this->belongsTo(Category::class, 'title_id', 'id');
    }
}
