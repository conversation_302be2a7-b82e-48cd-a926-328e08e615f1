<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Qualification extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $fillable = [
        'applicant_id',
        'qualification_type_id',
        'qulification_name',
        'university',
        'effective_date',
        'duration',
        'major_subject'
    ];

    protected $casts = [
        'effective_date' => 'date'
    ];

    /**
     * Get the applicant that owns the qualification.
     */
    public function applicant()
    {
        return $this->belongsTo(Applicant::class);
    }

    public function qualificationType()
    {
        return $this->belongsTo(Category::class, 'qualification_type_id', 'id');
    }
}
