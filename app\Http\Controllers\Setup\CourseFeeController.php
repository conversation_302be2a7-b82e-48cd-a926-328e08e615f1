<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Http\Requests\CourseFeeStoreRequest;
use App\Models\Course;
use App\Models\CourseFees;
use App\Models\IncomeType;
use Carbon\Carbon;
use Illuminate\Support\Facades\Gate;

class CourseFeeController extends Controller
{
    public static function middleware(): array
    {
        return [
            'auth',
        ];
    }

    public function CourseFeeIndex()
    {
        Gate::authorize('course.fee.list');
        $empNo = Auth()->user()->reg_no;

        if (Auth()->user()->hasRole(['Super-Admin', 'Admin','Admin-Officer','FGS-Admin'])) {

            $data = Course::where('active_status', 1)->where('course_main_category',41)->get();

        } elseif (Auth()->user()->hasRole(['Subject-Clerk','Subject-Assistant'])) {

            $data = Course::join('course_operators','courses.id','=','course_operators.course_code')
                    ->where('course_operators.emp_no', $empNo)
                    ->where('active_status', 1)
                    ->where('course_main_category',41)->get();

        }


        return view('admin.setups.course_fee.index', compact('data'));
    }

    public function CourseFeeShow($id)
    {
        Gate::authorize('course.fee.show');
        $courseId = decrypt($id);
        $courseFees = CourseFees::where('course_code', $courseId)->where('active_status', 1)->orderBy('reg_year', 'desc')->orderBy('batch', 'desc')->orderBy('pay_income_type_id')->get();
        $incomeTypes = IncomeType::all();
        $data = Course::find($courseId);

        //dd($studyBoardChairPersonDetails);
        return view('admin.setups.course_fee.show', compact('data', 'courseFees', 'incomeTypes'));
    }

    public function CourseFeeAllShow($id)
    {

        Gate::authorize('course.fee.all.show');
        $courseId = decrypt($id);
        $courseFees = CourseFees::where('course_code', $courseId)->where('active_status', 1)->where('pay_income_type_id','!=', 1)->orderBy('reg_year', 'desc')->orderBy('batch', 'desc')->orderBy('pay_income_type_id')->get();
        $data = Course::find($courseId);

        //dd($studyBoardChairPersonDetails);
        return view('admin.setups.course_fee.show_all', compact('data', 'courseFees'));
    }

    public function CourseFeeStore(CourseFeeStoreRequest $request)
    {
        Gate::authorize('course.fee.create');

        $data = $request->all();

        if (count($data['reg_year']) > 0) {
            foreach ($data['reg_year'] as $index => $reg_year) {

                $existingRecord = CourseFees::where([
                    'course_code' => $data['course_code'],
                    'reg_year' => $data['reg_year'][$index],
                    'batch' => $data['batch'][$index],
                    'intake' => $data['intake'][$index],
                    'pay_income_type_id' => $data['pay_income_type_id'][$index],
                    'active_status' => 1

                ])->first();

                if ($existingRecord) {
                    $existingRecord->update([
                        'updated_emp' => Auth()->user()->reg_no,
                        'updated_date' => Carbon::now(),
                        'active_status' => 0
                    ]);
                }

                CourseFees::create([
                    'course_code' => $data['course_code'],
                    'reg_year' => $data['reg_year'][$index],
                    'batch' => $data['batch'][$index],
                    'intake' => $data['intake'][$index],
                    'pay_income_type_id' => $data['pay_income_type_id'][$index],
                    'amount' => $data['amount'][$index],
                    'active_status' => 1,
                    'created_emp' => Auth()->user()->reg_no,
                    'created_date' => Carbon::now(),
                ]);
            }
        } else {
            $notification = [
                'message' => 'Please add at least one course fee record',
                'alert-type' => 'error'
            ];

            return redirect()->route('course.fee.show', encrypt($data['course_code']))->with($notification);
        }

        $notification = [
            'message' => 'New Course Fee Inserted or Updated Successfully',
            'alert-type' => 'success'
        ];

        return redirect()->route('course.fee.show', encrypt($data['course_code']))->with($notification);
    }


    public function CourseFeeDelete($id)
    {

        Gate::authorize('course.fee.delete');
        $courseFeeId = decrypt($id);
        $data = CourseFees::find($courseFeeId);
        $data->active_status = 0;
        $data->updated_emp = Auth()->user()->reg_no;
        $data->updated_date = Carbon::now();
        $data->save();

        $notification = array(
            'message' => 'Course Fee Data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('course.fee.show', encrypt($data->course_code))->with($notification);
    }
}
