<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategoryType4Seeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $datas =
            [
                ['name' => 'Qualification Type', 'slug' => 'qualification-type'],
                ['name' => 'Degree Class Type', 'slug' => 'degree-class-type'],
                ['name' => 'Document Type', 'slug' => 'document-type'],
                ['name' => 'Research Payment Type', 'slug' => 'research-payment-type'],
            ];

        foreach($datas as $data){
                DB::table('category_types')->insert($data);
        }
    }
}
