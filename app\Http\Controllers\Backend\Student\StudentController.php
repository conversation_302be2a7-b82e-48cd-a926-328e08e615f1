<?php

namespace App\Http\Controllers\Backend\Student;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\IncomeType;
use App\Models\RegisteredStudent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class StudentController extends Controller
{
   public function studentListOpen(Request $request)
   {

      $course_list = Course::where('course_main_category', 41)->get();

      if (isset($request->course) && isset($request->year) && isset($request->group)) {
         if ($request->course > 0 and $request->year != '' and $request->group > 0) {
            $data_text = RegisteredStudent::join('categories as t', 't.id', '=', 'registered_students.title_id')
               ->join('courses', 'courses.id', '=', 'registered_students.course_code')
               ->join('categories as st', 'st.id', '=', 'registered_students.active_status')
               ->select(
                  'registered_students.id',
                  'registered_students.reg_no_report',
                  't.category_name as title',
                  'registered_students.initials',
                  'registered_students.LName',
                  'registered_students.registered_year',
                  'registered_students.group',
                  'courses.course_name',
                  'st.category_name as active_status'
               )
               ->where('registered_students.course_code', $request->course)
               ->where('registered_students.registered_year', $request->year)
               ->where('registered_students.group', $request->group)
               ->orderBy('registered_students.reg_no_report')
               ->get();
         } else {
            $data_text = array();
         }
      } else {
         $data_text = array();
      }

      return view('admin.student.reg_student.studentList', compact('course_list', 'data_text'));
   }

   public function studentDetails($id)
   {

      $row_id = decrypt($id);

      $stud_text = RegisteredStudent::join('categories as t', 't.id', '=', 'registered_students.title_id')
         ->join('courses', 'courses.id', '=', 'registered_students.course_code')
         ->join('categories as g', 'g.id', '=', 'registered_students.gender_id')
         ->select(
            'registered_students.*',
            't.category_name as title',
            'courses.course_name',
            'g.category_name as gender'
         )
         ->where('registered_students.id', $row_id)
         ->get();

      if (count($stud_text) > 0) {
         foreach ($stud_text as $stud_texts) {
            $reg_no_new = $stud_texts->Reg_no;
            $title = $stud_texts->title;
            $initial = $stud_texts->initials;
            $LName = $stud_texts->LName;
            $course_name = $stud_texts->course_name;
            $reg_year = $stud_texts->registered_year;
            $reg_date = $stud_texts->reg_date;
            $reg_no_old = $stud_texts->reg_no_old;
            $reg_no_reprot = $stud_texts->reg_no_report;
            $FName = $stud_texts->FNames;
            $nic_old = $stud_texts->old_nic;
            $nic_new = $stud_texts->new_nic;
            $nic_active = $stud_texts->active_nic;
            $gender = $stud_texts->gender;
            $bdate = $stud_texts->bDate;
            $email = $stud_texts->email;
            $email_sjp = $stud_texts->sjp_email;
            $Profession = $stud_texts->profession;
            $permanent_add_line1 = $stud_texts->permanent_add_line1;
            $permanent_add_line2 = $stud_texts->permanent_add_line2;
            $permanent_add_line3 = $stud_texts->permanent_add_line3;
            $permanent_add_city = $stud_texts->permanent_add_city;
            $postal_add_line1 = $stud_texts->postal_add_line1;
            $postal_add_line2 = $stud_texts->postal_add_line2;
            $postal_add_line3 = $stud_texts->postal_add_line3;
            $postal_add_city = $stud_texts->postal_add_city;
            $mobile = $stud_texts->mobile;
            $homeTP = $stud_texts->home_tp;
            $otherTP = $stud_texts->other_tp;
            $indexNo = $stud_texts->index_no;
            $payRefNo = $stud_texts->payment_ref_no;
            $group = $stud_texts->group;
         }

         $response2 = Http::asForm()->post('http://************/API/APIpaymentsForId.php', [
            'StudentId' => $payRefNo,
            'key' => 'e2a5ee4a4f9dc023bdba233d9a8f1f8a'

         ]);

         if ($response2->successful()) {
            $dataPay = json_decode($response2->body(), true);

            $pay_data = collect($dataPay)->map(function ($item) {
               // Fetch the income type from the database using the nIncomeType value
               $incomeType = IncomeType::where('pay_income_type_code', $item['nIncomeType'])->first();

               // Prepare the result array for each item in dataPay
               return [
                  'income_type' => $incomeType ? $incomeType->pay_income_type_code : null, // Income type
                  'income_text' => $incomeType ? $incomeType->income_type_name : null, // Income type text
                  'amount' => $item['nAmount'], // Amount
                  'payDate' => $item['dPayDate'], // Payment Date
               ];
            })->toArray();
         } else {
            $pay_data = [];
         }
      } else {
         $reg_no_new = '';
         $title = '';
         $initial = '';
         $LName = '';
         $course_name = '';
         $reg_year = '';
         $reg_date = '';
         $reg_no_old = '';
         $reg_no_reprot = '';
         $FName = '';
         $nic_old = '';
         $nic_new = '';
         $nic_active = '';
         $gender = '';
         $bdate = '';
         $email = '';
         $email_sjp = '';
         $Profession = '';
         $permanent_add_line1 = '';
         $permanent_add_line2 = '';
         $permanent_add_line3 = '';
         $permanent_add_city = '';
         $postal_add_line1 = '';
         $postal_add_line2 = '';
         $postal_add_line3 = '';
         $postal_add_city = '';
         $mobile = '';
         $homeTP = '';
         $otherTP = '';
         $indexNo = '';
         $payRefNo = '';
         $group = '';
         $pay_data = [];
      }

      return view('admin.student.reg_student.studentDetails', compact(
         'reg_no_new',
         'title',
         'initial',
         'LName',
         'course_name',
         'reg_year',
         'reg_date',
         'reg_no_old',
         'reg_no_reprot',
         'FName',
         'nic_old',
         'nic_new',
         'nic_active',
         'gender',
         'bdate',
         'email',
         'email_sjp',
         'Profession',
         'permanent_add_line1',
         'permanent_add_line2',
         'permanent_add_line3',
         'permanent_add_city',
         'postal_add_line1',
         'postal_add_line2',
         'postal_add_line3',
         'postal_add_city',
         'mobile',
         'homeTP',
         'otherTP',
         'indexNo',
         'payRefNo',
         'group',
         'pay_data'
      ));
   }
}
