<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employee_records', function (Blueprint $table) {
            $table->integer('working_status')->after('applicant_id'); // 1 = Current Working, 2 = Previous Working
            $table->string('position')->nullable()->after('designation'); // Position/Rank
            $table->string('period')->nullable()->after('end_date'); // Employment period
            $table->text('nature_of_duty')->nullable()->after('period'); // Nature of duty
            $table->dropColumn(['employment_type_id', 'designation', 'institution', 'end_date','start_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employee_records', function (Blueprint $table) {
            $table->dropColumn(['working_status', 'position', 'period', 'nature_of_duty']);
            $table->integer('employment_type_id')->after('applicant_id');
            $table->string('designation')->after('employment_type_id');
            $table->string('institution')->after('designation');
            $table->date('start_date')->after('institution');
            $table->date('end_date')->after('start_date');
        });
    }
};
