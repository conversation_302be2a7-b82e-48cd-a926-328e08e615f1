<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class Category4Seeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $datas =
            [
                ['category_type_id' => 14, 'category_name' => 'Academic', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 14, 'category_name' => 'Professional', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],
                ['category_type_id' => 14, 'category_name' => 'Additional', 'display_name' => '','category_code'=> 3 ,'sorting_order' => 3],
                ['category_type_id' => 14, 'category_name' => 'Other', 'display_name' => '','category_code'=> 4 ,'sorting_order' => 4],

                ['category_type_id' => 15, 'category_name' => 'First Class', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 15, 'category_name' => 'Second Class Upper', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],
                ['category_type_id' => 15, 'category_name' => 'Second Class Lower', 'display_name' => '','category_code'=> 3 ,'sorting_order' => 3],
                ['category_type_id' => 15, 'category_name' => 'General', 'display_name' => '','category_code'=> 4 ,'sorting_order' => 4],

                ['category_type_id' => 16, 'category_name' => 'Profile Photo', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 16, 'category_name' => 'Degree Certificate', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],
                ['category_type_id' => 16, 'category_name' => 'Degree Trascript', 'display_name' => '','category_code'=> 3 ,'sorting_order' => 3],
                ['category_type_id' => 16, 'category_name' => 'Birth Certificate', 'display_name' => '','category_code'=> 4 ,'sorting_order' => 4],
                ['category_type_id' => 16, 'category_name' => 'NIC/Passport', 'display_name' => '','category_code'=> 5 ,'sorting_order' => 5],
                ['category_type_id' => 16, 'category_name' => 'Research Proposal/ Concept Paper 1', 'display_name' => '','category_code'=> 6 ,'sorting_order' => 6],
                ['category_type_id' => 16, 'category_name' => 'Research Proposal/ Concept Paper 2', 'display_name' => '','category_code'=> 7 ,'sorting_order' => 7],

                ['category_type_id' => 17, 'category_name' => 'First Year Payment', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 17, 'category_name' => 'Second Year Onwards Payment', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],
            ];

            foreach($datas as $data){
                DB::table('categories')->insert($data);
            }
    }
}
