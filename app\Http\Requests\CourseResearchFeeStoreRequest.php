<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CourseResearchFeeStoreRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }


    public function rules()
    {
        return [
            'course_code' => 'required|string|max:255',

            'reg_year' => 'required|array',
            'reg_year.*' => 'required|integer',

            'batch' => 'required|array',
            'batch.*' => 'required|integer',

            'intake' => 'required|array',
            'intake.*' => 'required|integer',

            'pay_income_type_id' => 'required|array',
            'pay_income_type_id.*' => 'required|integer',

            'research_payment_type_id' => 'required|array',
            'research_payment_type_id.*' => 'required|integer',

            'amount' => 'required|array',
            'amount.*' => 'required|numeric|min:0',
        ];
    }

    public function messages()
    {
        return [
            'course_code.required' => 'Course code is required',
            'reg_year.required' => 'Registration year is required',
            'reg_year.*.required' => 'Each registration year is required',
            'reg_year.*.integer' => 'Registration year must be an integer',
            'batch.required' => 'Batch is required',
            'batch.*.required' => 'Each batch is required',
            'batch.*.integer' => 'Batch must be an integer',
            'intake.required' => 'Intake is required',
            'intake.*.required' => 'Each intake is required',
            'intake.*.integer' => 'Intake must be an integer',
            'pay_income_type_id.required' => 'Income type is required',
            'pay_income_type_id.*.required' => 'Each income type is required',
            'pay_income_type_id.*.integer' => 'Income type must be an integer',
            'research_payment_type_id.required' => 'Research payment type is required',
            'research_payment_type_id.*.required' => 'Each research payment type is required',
            'research_payment_type_id.*.integer' => 'Research payment type must be an integer',
            'amount.required' => 'Amount is required',
            'amount.*.required' => 'Each amount is required',
            'amount.*.numeric' => 'Amount must be numeric',
            'amount.*.min' => 'Amount must be at least 0',
        ];
    }
}
