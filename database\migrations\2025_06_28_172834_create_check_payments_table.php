<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('check_payments', function (Blueprint $table) {
            $table->id();
            $table->integer('reg_no');
            $table->date('payment_date');
            $table->integer('check_no')->default(0);
            $table->double('amount')->default(0);
            $table->integer('enter_user')->default(0);
            $table->date('enter_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('check_payments');
    }
};
