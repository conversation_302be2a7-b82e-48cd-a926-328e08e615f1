<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('registered_students', function (Blueprint $table) {
            $table->integer('intake')->default(0)->after('group');
            $table->integer('max_pay_id')->default(0)->after('payment_ref_no');
            $table->integer('check_pay_available')->default(0)->after('max_pay_id');
            $table->integer('enter_user')->default(0)->after('old_data');
            $table->date('enter_date')->nullable()->after('enter_user');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('registered_students', function (Blueprint $table) {
            $table->dropColumn('intake');
            $table->dropColumn('max_pay_id');
            $table->dropColumn('check_pay_available');
            $table->dropColumn('enter_user');
            $table->dropColumn('enter_date');
        });
    }
};
