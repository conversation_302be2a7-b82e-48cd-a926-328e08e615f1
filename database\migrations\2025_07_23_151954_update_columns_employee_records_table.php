<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employee_records', function (Blueprint $table) {
            $table->string('postal_add1', 512)
                  ->change()
                  ->nullable(false);
            $table->renameColumn('postal_add1', 'address');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employee_records', function (Blueprint $table) {
            $table->string('address', 255)
                  ->change()
                  ->nullable(false);
            $table->renameColumn('address', 'postal_add1');
        });
    }
};
