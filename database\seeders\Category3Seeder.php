<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class Category3Seeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $datas =
            [
                ['category_type_id' => 12, 'category_name' => 'Sri Lankan Student', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 12, 'category_name' => 'Foreign Student', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],

                ['category_type_id' => 13, 'category_name' => 'Married', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 13, 'category_name' => 'Single', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],
                ['category_type_id' => 13, 'category_name' => 'Divorced', 'display_name' => '','category_code'=> 3 ,'sorting_order' => 3],
                ['category_type_id' => 13, 'category_name' => 'Widowed', 'display_name' => '','category_code'=> 4 ,'sorting_order' => 4],

            ];

            foreach($datas as $data){
                DB::table('categories')->insert($data);
            }
    }
}
