<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Http\Requests\CourseOpertorstoreRequest;
use App\Models\Course;
use App\Models\CourseOperator;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Support\Facades\Gate;

class CourseOperatorController extends Controller implements HasMiddleware
{
    public static function middleware(): array
    {
        return [
            'auth',
        ];
    }

    public function CourseOperatorIndex()
    {
        Gate::authorize('course.operator.list');
        $data = Course::where('active_status', 1)->get();

        return view('admin.setups.course_operator.index', compact('data'));
    }

    public function CourseOperatorShow($id)
    {
        Gate::authorize('course.operator.show');
        $courseId = decrypt($id);
        $courseOperators = CourseOperator::join('users', 'course_operators.emp_no', '=', 'users.reg_no')
                           ->select('course_operators.*', 'users.name as emp_name')
                           ->where('course_code', $courseId)
                           ->where('course_operators.active_status', 1)
                           ->get();
        $data = Course::find($courseId);

        return view('admin.setups.course_operator.show', compact('data','courseOperators'));
    }

    public function CourseOperatorAdd($id)
    {
        Gate::authorize('course.operator.create');
        $courseId = decrypt($id);
        $operators = User::join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('users.reg_no', 'users.name as emp_name', 'users.id', 'roles.name as role_name', 'roles.id as role_id')
                ->whereIn('roles.id', [8, 9])
                ->leftJoin('course_operators as vo', function($join) use ($courseId) {
                         $join->on('users.reg_no', '=', 'vo.emp_no')
                         ->where('vo.active_status', 1)
                         ->where('vo.course_code', $courseId);
                 })->whereNull('vo.emp_no')
                 ->distinct()
                 ->get();

        return view('admin.setups.course_operator.add',compact('courseId','operators'));
    }

    public function CourseOperatorStore(CourseOpertorstoreRequest $request)
    {
        Gate::authorize('course.operator.create');
        $data = CourseOperator::create($request->prepareData());

        $notification = array(
            'message' => 'New Course Operator Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('course.operator.show', encrypt($data->course_code))->with($notification);
    }

    public function CourseOperatorDelete($id)
    {
        Gate::authorize('course.operator.delete');
        $courseOperatorId = decrypt($id);
        $data = CourseOperator::find($courseOperatorId);
        $data->active_status = 0;
        $data->end_date = date('Y-m-d');
        $data->updated_emp = Auth()->user()->reg_no;
        $data->updated_date = Carbon::now();
        $data->save();
        //$data->delete();

        $notification = array(
            'message' => 'Course Operator Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('course.operator.show', encrypt($data->course_code))->with($notification);
    }
}
