<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $datas =
            [
                ['id' => 3000, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'PhD', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 20, 'study_board_id' => 1, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3001, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'PhD', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 20, 'study_board_id' => 2, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3002, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'PhD', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 20, 'study_board_id' => 3, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3003, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'PhD', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 20, 'study_board_id' => 4, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3004, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 44, 'course_name' => 'PhD', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 20, 'study_board_id' => 5, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3005, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'PhD', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 20, 'study_board_id' => 6, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3006, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'PhD', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 20, 'study_board_id' => 7, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3007, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'PhD', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 20, 'study_board_id' => 8, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3008, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'PhD', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 20, 'study_board_id' => 9, 'active_status' => 1, 'created_at' => now()],

                ['id' => 3009, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MPhil', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 21, 'study_board_id' => 1, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3010, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MPhil', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 21, 'study_board_id' => 2, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3011, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MPhil', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 21, 'study_board_id' => 3, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3012, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MPhil', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 21, 'study_board_id' => 4, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3013, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 44, 'course_name' => 'MPhil', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 21, 'study_board_id' => 5, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3014, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MPhil', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 21, 'study_board_id' => 6, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3015, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MPhil', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 21, 'study_board_id' => 7, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3016, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MPhil', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 21, 'study_board_id' => 8, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3017, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MPhil', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 21, 'study_board_id' => 9, 'active_status' => 1, 'created_at' => now()],

                ['id' => 3018, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MAR', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 22, 'study_board_id' => 1, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3019, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MAR', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 22, 'study_board_id' => 2, 'active_status' => 1, 'created_at' => now()],

                ['id' => 3020, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MAT', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 23, 'study_board_id' => 1, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3021, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MAT', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 23, 'study_board_id' => 2, 'active_status' => 1, 'created_at' => now()],

                ['id' => 3022, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MAQ', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 24, 'study_board_id' => 1, 'active_status' => 1, 'created_at' => now()],
                ['id' => 3023, 'previous_course_code' => NULL, 'course_main_category' => 42, 'course_application_open_method' => 43, 'course_name' => 'MAQ', 'medium_cat_id' => 18, 'payment_course_code' => 1234, 'application_acc_number' => NULL, 'course_cat_id' => 24, 'study_board_id' => 2, 'active_status' => 1, 'created_at' => now()],


                [
                    'id' => 3100,
                    'previous_course_code' => 5175,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Post Graduate Diploma in Writership & Communication Programme',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 587,
                    'application_acc_number' => '0053010005873',
                    'course_cat_id' => 27,
                    'study_board_id' => 1,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3101,
                    'previous_course_code' => 6246,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Post Graduate Diploma in Drama & Theater',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 633,
                    'application_acc_number' => null,
                    'course_cat_id' => 27,
                    'study_board_id' => 1,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3102,
                    'previous_course_code' => 9133,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Master of Arts in Teaching English as a Second Language',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 678,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 1,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3103,
                    'previous_course_code' => 5365,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Post Graduate Diploma in Criminology & Criminal Justice',
                    'medium_cat_id' => 19,
                    'payment_course_code' => 586,
                    'application_acc_number' => null,
                    'course_cat_id' => 27,
                    'study_board_id' => 2,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3104,
                    'previous_course_code' => 5533,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Post Graduate Diploma In Sociology',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 590,
                    'application_acc_number' => '053010005907',
                    'course_cat_id' => 27,
                    'study_board_id' => 2,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3105,
                    'previous_course_code' => 9112,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Post Graduate Diploma in Social Statistics',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 668,
                    'application_acc_number' => '053010006681',
                    'course_cat_id' => 27,
                    'study_board_id' => 2,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3106,
                    'previous_course_code' => 9113,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Post Graduate Diploma in Business Statistics',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 669,
                    'application_acc_number' => '053010006699',
                    'course_cat_id' => 27,
                    'study_board_id' => 2,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3107,
                    'previous_course_code' => 9145,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Master of Sociology',
                    'medium_cat_id' => 19,
                    'payment_course_code' => 684,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 2,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3108,
                    'previous_course_code' => 9193,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Master of Economics',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 514,
                    'application_acc_number' => '053010005147',
                    'course_cat_id' => 25,
                    'study_board_id' => 2,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3109,
                    'previous_course_code' => 9196,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Master of Criminal Justice and Corrections',
                    'medium_cat_id' => 19,
                    'payment_course_code' => 515,
                    'application_acc_number' => '053010005154',
                    'course_cat_id' => 25,
                    'study_board_id' => 2,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3110,
                    'previous_course_code' => 5193,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Forestry & Environmental Management',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 507,
                    'application_acc_number' => '053010005071',
                    'course_cat_id' => 25,
                    'study_board_id' => 3,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3111,
                    'previous_course_code' => 5370,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc / PGD in Fisheries and Aquatic Resources Management',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 504,
                    'application_acc_number' => null,
                    'course_cat_id' => 26,
                    'study_board_id' => 3,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3112,
                    'previous_course_code' => 5544,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Food Science & Technology',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 505,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 3,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3113,
                    'previous_course_code' => 6134,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Science & Technology Herbal Products',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 569,
                    'application_acc_number' => '053010005691',
                    'course_cat_id' => 25,
                    'study_board_id' => 3,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3114,
                    'previous_course_code' => 9177,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Industrial Organic Chemistry',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 509,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 3,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3115,
                    'previous_course_code' => 9178,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Master of Industrial Organic Chemistry',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 510,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 3,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3116,
                    'previous_course_code' => 9179,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Post Graduate Diploma in Industrial Organic Chemistry',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 511,
                    'application_acc_number' => null,
                    'course_cat_id' => 27,
                    'study_board_id' => 3,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3117,
                    'previous_course_code' => 5528,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc / PGD in Industrial Mathematics',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 565,
                    'application_acc_number' => '053010005659',
                    'course_cat_id' => 26,
                    'study_board_id' => 4,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3118,
                    'previous_course_code' => 5546,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Polymer Science & Technology',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 567,
                    'application_acc_number' => '053010005675',
                    'course_cat_id' => 25,
                    'study_board_id' => 4,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3119,
                    'previous_course_code' => 6037,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Computer Science',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 503,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 4,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3120,
                    'previous_course_code' => 6117,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc / PGD in Industrial Analytical Chemistry',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 572,
                    'application_acc_number' => null,
                    'course_cat_id' => 26,
                    'study_board_id' => 4,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3121,
                    'previous_course_code' => 9123,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Applied Statistics',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 658,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 4,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3122,
                    'previous_course_code' => 9134,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Postgraduate Certificate in Applied Statistics',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 673,
                    'application_acc_number' => '053010006731',
                    'course_cat_id' => 28,
                    'study_board_id' => 4,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3123,
                    'previous_course_code' => 9152,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Data Science and Artificial Intelligence',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 685,
                    'application_acc_number' => '053010006855',
                    'course_cat_id' => 25,
                    'study_board_id' => 4,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3124,
                    'previous_course_code' => 5266,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MBA / MSc in Management',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 566,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 5,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3125,
                    'previous_course_code' => 5501,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc / PGD in Real Estate Management & Valuation',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 568,
                    'application_acc_number' => null,
                    'course_cat_id' => 26,
                    'study_board_id' => 5,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3126,
                    'previous_course_code' => 5710,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc / PGD in Applied Finance',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 571,
                    'application_acc_number' => null,
                    'course_cat_id' => 26,
                    'study_board_id' => 5,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3127,
                    'previous_course_code' => 6024,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Master of Entrepreneurship',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 588,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 5,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3128,
                    'previous_course_code' => 9054,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Master of Professional Accounting (MPAcc)',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 592,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 5,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3129,
                    'previous_course_code' => 9066,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Master of Public Management',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 674,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 5,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3130,
                    'previous_course_code' => 9067,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Public Policy and Management',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 675,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 5,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3131,
                    'previous_course_code' => 9057,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'Post Graduate Diploma in Monitoring and Evaluation',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 637,
                    'application_acc_number' => null,
                    'course_cat_id' => 27,
                    'study_board_id' => 6,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3132,
                    'previous_course_code' => 9188,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Immunology',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 512,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 6,
                    'active_status' => 1,
                    'created_at' => now()
                ],
                [
                    'id' => 3133,
                    'previous_course_code' => 5998,
                    'course_main_category' => 41,
                    'course_application_open_method' => 44,
                    'course_name' => 'MSc in Programme GIS & Remote Sensing',
                    'medium_cat_id' => 18,
                    'payment_course_code' => 506,
                    'application_acc_number' => null,
                    'course_cat_id' => 25,
                    'study_board_id' => 7,
                    'active_status' => 1,
                    'created_at' => now()
                ]

            ];

        foreach ($datas as $data) {
            DB::table('courses')->insert($data);
        }
    }
}
