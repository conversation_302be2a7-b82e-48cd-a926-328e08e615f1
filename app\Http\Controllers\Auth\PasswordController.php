<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\PasswordUpdateMail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\Rules\Password;

class PasswordController extends Controller
{
    /**
     * Update the user's password.
     */
    public function update(Request $request): RedirectResponse
    {
        $validated = $request->validateWithBag('updatePassword', [
            'current_password' => ['required', 'current_password'],
            'password' => ['required', Password::defaults(), 'confirmed'],
        ]);

        $request->user()->update([
            'password' => Hash::make($validated['password']),
            'password_change_status' => 1,
        ]);

        try {
            Mail::to($request->user()->email)->send(new PasswordUpdateMail($request->user()));
        } catch (\Exception $e) {
            // Log the error but don't expose it to the user
            Log::error('Failed to send password update email: ' . $e->getMessage());
        }

        return back()->with('status', 'password-updated');
    }
}
