<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applicants', function (Blueprint $table) {
            //$table->renameColumn('permanent_city_id', 'permanent_city');
            //$table->renameColumn('current_city_id', 'current_city');
            $table->renameColumn('duration_cat_id', 'duration');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applicants', function (Blueprint $table) {
            //$table->renameColumn('permanent_city', 'permanent_city_id');
            //$table->renameColumn('current_city', 'current_city_id');
            $table->renameColumn('duration', 'duration_cat_id');
        });
    }
};
