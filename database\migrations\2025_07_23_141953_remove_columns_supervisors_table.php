<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('supervisors', function (Blueprint $table) {
            $table->dropColumn('permanent_add2');
            $table->dropColumn('permanent_add3');
            $table->dropColumn('permanent_city_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('supervisors', function (Blueprint $table) {
            $table->string('permanent_add2')->nullable()->after('permanent_add1');
            $table->string('permanent_add3')->nullable()->after('permanent_add2');
            $table->integer('permanent_city_id')->default(0)->after('permanent_add3');
        });
    }
};
