<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('registered_students', function (Blueprint $table) {
            //
            $table->dropColumn('intake');
            $table->dropColumn('reg_year');
            $table->dropColumn('active');
            $table->dropColumn('student_acc_id');
            $table->dropColumn('reg_no_report');

            $table->integer('course_code')->default(0)->after('id');
            $table->integer('subject_code')->default(0)->after('course_code');
            $table->integer('registered_year')->default(0)->after('subject_code');
            $table->integer('group')->default(0)->after('registered_year');
            $table->integer('Reg_no')->after('group');
            $table->string('reg_no_old')->nullable()->after('Reg_no');
            $table->string('index_no')->nullable()->after('reg_no_old');
            $table->date('reg_date')->after('index_no');
            $table->integer('payment_ref_no')->default(0)->after('reg_date');
            
            $table->integer('title_id')->default(0)->after('active_nic');
            $table->text('initials')->nullable()->after('title_id');
            $table->text('LName')->nullable()->after('initials');
            $table->text('FNames')->nullable()->after('LName');
            $table->integer('gender_id')->default(0)->after('FNames');
            $table->date('bDate')->nullable()->after('gender_id');
            $table->text('email')->nullable()->after('bDate');
            $table->text('sjp_email')->nullable()->after('email');
            $table->text('profession')->nullable()->after('sjp_email');
            $table->string('mobile')->nullable()->after('profession');
            $table->string('home_tp')->nullable()->after('mobile');
            $table->string('other_tp')->nullable()->after('home_tp');
            $table->text('postal_add_line1')->nullable()->after('other_tp');
            $table->text('postal_add_line2')->nullable()->after('postal_add_line1');
            $table->text('postal_add_line3')->nullable()->after('postal_add_line2');
            $table->text('postal_add_city')->nullable()->after('postal_add_line3');
            $table->text('permanent_add_line1')->nullable()->after('postal_add_city');
            $table->text('permanent_add_line2')->nullable()->after('permanent_add_line1');
            $table->text('permanent_add_line3')->nullable()->after('permanent_add_line2');
            $table->text('permanent_add_city')->nullable()->after('permanent_add_line3');
            $table->integer('application_process_id')->default(0)->after('permanent_add_city');
            $table->integer('open_application_id')->default(0)->after('application_process_id');
            $table->string('application_ref_no')->nullable()->after('open_application_id');
            $table->text('ref1_name')->nullable()->after('application_ref_no');
            $table->text('ref1_email')->nullable()->after('ref1_name');
            $table->string('ref1_mobile')->nullable()->after('ref1_email');
            $table->text('ref2_name')->nullable()->after('ref1_mobile');
            $table->text('ref2_email')->nullable()->after('ref2_name');
            $table->string('ref2_mobile')->nullable()->after('ref2_email');
            $table->text('first_degree_name')->nullable()->after('ref2_mobile');
            $table->text('major_subject')->nullable()->after('first_degree_name');
            $table->text('univercity')->nullable()->after('major_subject');
            $table->integer('class_cat_id')->default(0)->after('univercity');
            $table->date('first_degree_effective_date')->nullable()->after('class_cat_id');
            $table->integer('duration_cat_id')->default(0)->after('first_degree_effective_date');
            $table->double('gpa')->nullable()->after('duration_cat_id');
            $table->date('terminate_date')->nullable()->after('gpa');
            $table->integer('convocation_status')->default(0)->after('terminate_date');
            $table->date('convocation_date')->nullable()->after('convocation_status');
            $table->integer('convocation_id')->default(0)->after('convocation_date');
            $table->string('convocation_no')->nullable()->after('convocation_id');
            $table->integer('active_status')->default(0)->after('convocation_no');


        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('registered_students', function (Blueprint $table) {
            //
            $table->string('intake')->after('active_nic');
            $table->string('reg_year')->after('intake');            
            $table->integer('active')->after('reg_year');
            $table->integer('student_acc_id')->after('active');
            $table->string('reg_no_report')->after('student_acc_id');

            $table->dropColumn('course_code');
            $table->dropColumn('subject_code');
            $table->dropColumn('registered_year');
            $table->dropColumn('group');
            $table->dropColumn('Reg_no');
            $table->dropColumn('reg_no_old');
            $table->dropColumn('index_no');
            $table->dropColumn('reg_date');
            $table->dropColumn('payment_ref_no');
            
            $table->dropColumn('title_id');
            $table->dropColumn('initials');
            $table->dropColumn('LName');
            $table->dropColumn('FNames');
            $table->dropColumn('gender_id');
            $table->dropColumn('bDate');
            $table->dropColumn('email');
            $table->dropColumn('sjp_email');
            $table->dropColumn('profession');
            $table->dropColumn('mobile');
            $table->dropColumn('home_tp');
            $table->dropColumn('other_tp');
            $table->dropColumn('postal_add_line1');
            $table->dropColumn('postal_add_line2');
            $table->dropColumn('postal_add_line3');
            $table->dropColumn('postal_add_city');
            $table->dropColumn('permanent_add_line1');
            $table->dropColumn('permanent_add_line2');
            $table->dropColumn('permanent_add_line3');
            $table->dropColumn('permanent_add_city');
            $table->dropColumn('application_process_id');
            $table->dropColumn('open_application_id');
            $table->dropColumn('application_ref_no');
            $table->dropColumn('ref1_name');
            $table->dropColumn('ref1_email');
            $table->dropColumn('ref1_mobile');
            $table->dropColumn('ref2_name');
            $table->dropColumn('ref2_email');
            $table->dropColumn('ref2_mobile');
            $table->dropColumn('first_degree_name');
            $table->dropColumn('major_subject');
            $table->dropColumn('univercity');
            $table->dropColumn('class_cat_id');
            $table->dropColumn('first_degree_effective_date');
            $table->dropColumn('duration_cat_id');
            $table->dropColumn('gpa');
            $table->dropColumn('terminate_date');
            $table->dropColumn('convocation_status');
            $table->dropColumn('convocation_date');
            $table->dropColumn('convocation_id');
            $table->dropColumn('convocation_no');
            $table->dropColumn('active_status');


        });
    }
};
