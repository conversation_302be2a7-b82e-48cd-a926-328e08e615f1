<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class DeactiveAccountMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

        public function __construct()
    {
        //
    }


    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Account Deactivation Notification',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'emails.deactivate-account',
        );
    }

    public function attachments(): array
    {
        return [];
    }
}
