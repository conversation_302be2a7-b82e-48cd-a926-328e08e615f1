<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('first_supervisors', function (Blueprint $table) {
            $table->id();
            $table->integer('reg_no');
            $table->integer('emp_no');
            $table->integer('dep_id');
            $table->text('dep_name')->nullable();
            $table->integer('added_by');
            $table->date('added_date');
            $table->integer('removed_by')->default(0);
            $table->date('removed_date')->nullable();
            $table->text('remove_reason')->nullable();            
            $table->integer('status')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('first_supervisors');
    }
};
