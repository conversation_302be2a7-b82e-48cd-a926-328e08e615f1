<?php

namespace App\Http\Requests;

use App\Models\CourseCoordinator;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class CourseCoordinatorStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'course_code' => 'required',
            'emp_no' => 'required',
            'appointment_type_id' => 'required',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ];
    }

    public function messages()
    {
        return [
            'course_code.required' => 'Select relavent course',
            'emp_no.required' => 'Select relavent course coordinator',
            'appointment_type_id.required' => 'Select relavent appointment type',
            'start_date.required' => 'Select relavent appointment date',
            'end_date.after_or_equal' => 'The termination date must be after or equal to the appointment date.',
            'end_date.required' => 'Select relevant termination date',
        ];
    }

    public function prepareData()
    {
        $selectedEmployee = explode('|', $this->emp_no);
        $employeeNo = $selectedEmployee[0];

        return [
            'course_code' => $this->course_code,
            'emp_no' => $employeeNo,
            'appointment_type_id' => $this->appointment_type_id,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'active_status' => 1,
            'created_emp' => $this->user()->reg_no,
            'created_date' => Carbon::now(),
        ];
    }

    public function persist($id)
    {
        $selectedEmployee = explode('|', $this->emp_no);
        $employeeNo = $selectedEmployee[0];
        $courseCoordinator = CourseCoordinator::findOrFail($id);

        if ($courseCoordinator->emp_no != $employeeNo) {
            // Set the existing record's active_status to 0
            $courseCoordinator->active_status = 0;
            $courseCoordinator->updated_emp = $this->user()->reg_no;
            $courseCoordinator->updated_date = Carbon::now();
            $courseCoordinator->save();

            // Create a new record
            $newcourseCoordinator = new CourseCoordinator();
            $newcourseCoordinator->course_code = $this->course_code;
            $newcourseCoordinator->emp_no = $employeeNo;
            $newcourseCoordinator->appointment_type_id = $this->appointment_type_id;
            $newcourseCoordinator->start_date = $this->start_date;
            $newcourseCoordinator->end_date = $this->end_date;
            $newcourseCoordinator->active_status = 1;
            $newcourseCoordinator->created_emp = $this->user()->reg_no;
            $newcourseCoordinator->created_date = Carbon::now();
            $newcourseCoordinator->save();

            return $newcourseCoordinator;

        } else {
            // Update the existing record
            $courseCoordinator->appointment_type_id = $this->appointment_type_id;
            $courseCoordinator->start_date = $this->start_date;
            $courseCoordinator->end_date = $this->end_date;
            $courseCoordinator->updated_emp = $this->user()->reg_no;
            $courseCoordinator->updated_date = Carbon::now();
            $courseCoordinator->save();

            return $courseCoordinator;
        }
    }
}
